/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sm_chat.c
 * @description: 待销毁，已经合并到sm_call.c.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include "sk_common.h"
#include "sk_log.h"
#include "sk_os.h"
#include "sk_board.h"
#include "sk_sm.h"
#include "sk_audio.h"
#include "sk_clink.h"
#include "sk_rlink.h"
#include "sk_opus_dec.h"
#include "sm.h"

#define TAG "SmChat"

enum {
    SM_CHAT_STATE_IDLE,
    SM_CHAT_STATE_WAIT_CONTROLLER,
    SM_CHAT_STATE_WAIT_AGENT,
    SM_CHAT_STATE_CALLING,
};

typedef struct {
    int16_t currSessionId;  // 内部的SessionID, Clink， Rlink可见.
    int32_t tickCnt;
    int32_t state;
    SkStateHandler handler;
    SkSmStateEndCallback endCb;
} SmChatCtrl;

SmChatCtrl g_chatSmCtrl;

int32_t SmChatStart(SkSubStateInfo *info, const SkSmEvent *event) {
    SmChatCtrl *ctrl = (SmChatCtrl *)info->privateData;
    uint8_t audioList[2];

    SkRledSetEvent(SK_LED_EVENT_CHAT);
    if (ctrl->state != SM_CHAT_STATE_IDLE) {
        SK_LOGE(TAG, "Already in chat state");
        return SK_RET_FAIL;
    }
    SkPlayerResume();
    audioList[0] = AUDIO_IDX_WAIT;
    SkSmPlayLocalNotice(audioList, 1, false);
    ctrl->currSessionId = SkSmNewSession(ctrl->handler);
    ctrl->tickCnt = 0;
    ctrl->state = SM_CHAT_STATE_WAIT_CONTROLLER;
    SK_LOGE(TAG, "Start chat");
    SkClinkEventNotify(CLINK_EVENT_CALL_AGENT, ctrl->currSessionId, 0, 0);
    SkRecorderResume();
    SkVcProcessEnable(true);
    SkSrProcessEnable(false);
    SkOpusDecUnmuteRemote();
    SkOsShowCurrTime();

    return SK_RET_SUCCESS;
}

void SmChatReset(SmChatCtrl *ctrl) {
    SkRlinkEventNotify(RLINK_EVENT_STOP_CALL, 0);
    SkClinkEventNotify(CLINK_EVENT_FINISH_CALL, ctrl->currSessionId, 0, 0);
    SkVcProcessEnable(false);
    SkSrProcessEnable(true);
    ctrl->state = SM_CHAT_STATE_IDLE;
    return;
}

int32_t SmChatStop(SkSubStateInfo *info) {
    SmChatCtrl *ctrl = (SmChatCtrl *)info->privateData;
    SmChatReset(ctrl);
    SK_LOGE(TAG, "Stop chat");
    return SK_RET_SUCCESS;
}

void SmChatInnerStop(SmChatCtrl *ctrl) {
    SmChatReset(ctrl);
    if (ctrl->endCb != NULL) {
        ctrl->endCb(ctrl->handler, STATE_CHAT);
    }
}

int32_t SmChatEventProc(SkSubStateInfo *info, const SkSmEvent *event) {
    SmChatCtrl *ctrl = (SmChatCtrl *)info->privateData;

    if (event->event == SM_EVENT_CMD) {
        switch (event->subEvent) {
            case SPEECH_CMD_EVENT_PAUSE:
                SkPlayerPause();
                SkRecorderPause();
                SK_LOGI(TAG, "Pause");
                break;
            case SPEECH_CMD_EVENT_RESUME:
                SkPlayerResume();
                SkRecorderResume();
                SK_LOGI(TAG, "Resume");
                break;
            case SPEECH_CMD_EVENT_QUIT:
                SkSmActionOk();
                SmChatInnerStop(ctrl);
                SK_LOGI(TAG, "Quit");
                break;
            default:
                SK_LOGE(TAG, "Unknown cmd %d", event->subEvent);
                return SK_RET_FAIL;
        }
    } else if (event->event == SM_EVENT_LINK) {
        if (event->subEvent == SM_EVENT_CLINK_RELAY_OK) {
            if ((ctrl->state == SM_CHAT_STATE_WAIT_CONTROLLER) && (event->param1 == ctrl->currSessionId)) {
                SkRlinkEventNotify(RLINK_EVENT_START_CALLER_CMD, ctrl->currSessionId);
                ctrl->state = SM_CHAT_STATE_WAIT_AGENT;
            }
        } else if (event->subEvent == SM_EVENT_CLINK_RELAY_FAIL) {
            if (ctrl->state == SM_CHAT_STATE_WAIT_CONTROLLER) {
                SmChatInnerStop(ctrl);
            }
        } else if (event->subEvent == SM_EVENT_RLINK_PEER_CONNECT) {
            if ((ctrl->state == SM_CHAT_STATE_WAIT_AGENT) && (event->param1 == ctrl->currSessionId)) {
                ctrl->state = SM_CHAT_STATE_CALLING;
            }
        } else if (event->subEvent == SM_EVENT_RLINK_DISCONNECT) {
            SkClinkEventNotify(CLINK_EVENT_FINISH_CALL, 0, 0, 0);
        } else if (event->subEvent == SM_EVENT_CLINK_DISCONNECT) {
            if (ctrl->state == SM_CHAT_STATE_CALLING || ctrl->state == SM_CHAT_STATE_WAIT_AGENT) {
                SmChatInnerStop(ctrl);
            }
        } else if (event->subEvent == SM_EVENT_CLINK_CALL_REQUEST) {
            // 收到请求暂时不处理
        } else if (event->subEvent == SM_EVENT_RLINK_ALIGN_REPLY) {
            // 收到对齐回复
            SkSmActionDone();
            SkOpusDecUnmuteRemote(); // 解除静音
        }
    } else if (event->event == SM_EVENT_SYSTEM) {
        if (event->subEvent == SM_EVENT_SYSTEM_TICK) {
            if (ctrl->state == SM_CHAT_STATE_WAIT_CONTROLLER || 
                ctrl->state == SM_CHAT_STATE_WAIT_AGENT) {
                ctrl->tickCnt++;
                if (ctrl->tickCnt >= 30) {
                    SK_LOGE(TAG, "Timeout, stop chat");
                    SmChatInnerStop(ctrl);
                }
            }
        }
    }
    return SK_RET_SUCCESS;
}

int32_t SkSmChatInit(SkSmItem *item, SkSmStateEndCallback endCb, SkStateHandler handler) {
    g_chatSmCtrl.state = SM_CHAT_STATE_IDLE;
    g_chatSmCtrl.currSessionId = 0;
    g_chatSmCtrl.endCb = endCb;
    g_chatSmCtrl.handler = handler;
    item->info.subState = 0;
    item->info.privateData = &g_chatSmCtrl;
    item->startProc = SmChatStart;
    item->stopProc = SmChatStop;
    item->eventProc = SmChatEventProc;
    return SK_RET_SUCCESS;
}
