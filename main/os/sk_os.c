/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_os.c
 * @description: 操作系统适配.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <time.h>
#include <sys/time.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_freertos_hooks.h"
#include "esp_log.h"
#include "esp_pm.h"
#include "esp_heap_caps.h"
#include "sk_os.h"

#define TAG "SkOs"

extern int esp_clk_cpu_freq();

typedef struct {
    size_t internalRamSize;
    size_t spiRamSize;
} SkOsStatMemInfo;

SkOsStatMemInfo g_skOsStatMemInfo;

void SkOsGetTaskNames() {
#ifdef configUSE_TRACE_FACILITY
#ifdef CONFIG_SK_CPU_USAGE_SHOW
    TaskStatus_t * pxTaskStatusArray;
    UBaseType_t uxArraySize, x, maxCnt;
    configRUN_TIME_COUNTER_TYPE ulTotalTime;

    /* Take a snapshot of the number of tasks in case it changes while this
     * function is executing. */
    maxCnt = 32;
    uxArraySize = maxCnt;

    /* Allocate an array index for each task.  NOTE!  If
     * configSUPPORT_DYNAMIC_ALLOCATION is set to 0 then pvPortMalloc() will
     * equate to NULL. */
    pxTaskStatusArray = malloc( maxCnt * sizeof( TaskStatus_t)); /*lint !e9079 All values returned by pvPortMalloc() have at least the alignment required by the MCU's stack and this allocation allocates a struct that has the alignment requirements of a pointer. */

    if (pxTaskStatusArray != NULL) {
        x = 0;
        uxArraySize = uxTaskGetSystemState(pxTaskStatusArray, uxArraySize, &ulTotalTime);
        ESP_LOGI(TAG, "StackBase\t\tTaskName");
        for (x = 0; x < uxArraySize; x++) {
            ESP_LOGI(TAG, "0x%08x\t\t%s", (uint32_t)pxTaskStatusArray[x].pxStackBase, pxTaskStatusArray[x].pcTaskName);
        }

        /* Free the array again.  NOTE!  If configSUPPORT_DYNAMIC_ALLOCATION
         * is 0 then vPortFree() will be #defined to nothing. */
        free(pxTaskStatusArray);
    }
#endif
#endif
    return;
}


int32_t SkOsGetCpuUsageArray(uint32_t *stackBase, uint16_t *cpuCnt, uint32_t *totalCnt, uint32_t maxCnt) {
#ifdef configUSE_TRACE_FACILITY
#ifdef CONFIG_SK_CPU_USAGE_SHOW
    TaskStatus_t * pxTaskStatusArray;
    UBaseType_t uxArraySize, x;
    configRUN_TIME_COUNTER_TYPE ulTotalTime, ulStatsAsPercentage;

    /* Take a snapshot of the number of tasks in case it changes while this
     * function is executing. */
    uxArraySize = maxCnt;
    x = 0;

    /* Allocate an array index for each task.  NOTE!  If
     * configSUPPORT_DYNAMIC_ALLOCATION is set to 0 then pvPortMalloc() will
     * equate to NULL. */
    pxTaskStatusArray = malloc( maxCnt * sizeof( TaskStatus_t)); /*lint !e9079 All values returned by pvPortMalloc() have at least the alignment required by the MCU's stack and this allocation allocates a struct that has the alignment requirements of a pointer. */

    if (pxTaskStatusArray != NULL) {
        uxArraySize = uxTaskGetSystemState(pxTaskStatusArray, uxArraySize, &ulTotalTime);
        /* For percentage calculations. */
        ulTotalTime /= 100UL;
        *totalCnt = ulTotalTime;

        /* Avoid divide by zero errors. */
        if (ulTotalTime > 0UL) {
            /* Create a human readable table from the binary data. */
            for (x = 0; x < uxArraySize; x++) {
                /* What percentage of the total run time has the task used?
                 * This will always be rounded down to the nearest integer.
                 * ulTotalRunTime has already been divided by 100. */
                ulStatsAsPercentage = pxTaskStatusArray[ x ].ulRunTimeCounter / ulTotalTime;

                if (x >= maxCnt) {
                    break;
                }
                stackBase[x] = (uint32_t)pxTaskStatusArray[x].pxStackBase;
                cpuCnt[x] = ulStatsAsPercentage;
            }
        }

        /* Free the array again.  NOTE!  If configSUPPORT_DYNAMIC_ALLOCATION
         * is 0 then vPortFree() will be #defined to nothing. */
        free(pxTaskStatusArray);
    }

    return (int32_t)x;
#else
    return 0;
#endif
#else
    return 0;
#endif
}

void SkOsShowMemUsage() {
    size_t internalRam = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
    size_t spiRam = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
    ESP_LOGI(TAG, "Free Internal Heap: %ld bytes", esp_get_free_internal_heap_size());
    ESP_LOGI(TAG, "Internal RAM left: %.2f KB", (float)internalRam / 1024);
    ESP_LOGI(TAG, "SPI RAM left: %.2f KB", (float)spiRam / 1024);
}

void SkOsStatModuleMem(char *name, bool endFlag) {
    size_t internalRam = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
    size_t spiRam = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);

    if (endFlag) {
        if (spiRam > g_skOsStatMemInfo.spiRamSize) {
            g_skOsStatMemInfo.spiRamSize = spiRam;
        }
        ESP_LOGI(TAG, "Moduloe %s: IRAM: %dB, SpiRAM: %dKB, freeIRAM: %dB, freeSpiRAM: %dKB", name,
            g_skOsStatMemInfo.internalRamSize - internalRam, 
            (g_skOsStatMemInfo.spiRamSize - spiRam) / 1024,
            internalRam, spiRam / 1024);
    }

    g_skOsStatMemInfo.internalRamSize = internalRam;
    g_skOsStatMemInfo.spiRamSize = spiRam;
    return;
}

void SkOsReboot() {
    ESP_LOGI(TAG, "System reboot");
    esp_restart();

    return;
}

void SkOsShowCurrTime() {
    struct tm *timeinfo = NULL;
    struct timeval tv;
    char buffer[80];

    // 获取当前时间（包括毫秒）
    gettimeofday(&tv, NULL);

    // 将秒部分转换为本地时间
    timeinfo = localtime(&tv.tv_sec);

    // 格式化时间字符串
    strftime(buffer, sizeof(buffer), "%Y-%m-%d %H:%M:%S", timeinfo);
    ESP_LOGI(TAG, "Tick: %u, Current time: %s.%03ld", SkOsGetTickCnt(), buffer, tv.tv_usec / 1000);
    return;
}

void SkOsGetTimeInfo(uint64_t *sec, uint32_t *msec, uint32_t *tickCnt) {
    struct timeval tv;

    gettimeofday(&tv, NULL);
    *tickCnt = SkOsGetTickCnt();
    *sec = tv.tv_sec;
    *msec = tv.tv_usec / 1000;

    return;
}

void SkOsShowSysInfo() {
    int cpu_freq = esp_clk_cpu_freq();

    ESP_LOGI(TAG, "CPU Frequency: %d Hz", cpu_freq);
#ifdef CONFIG_SK_CPU_USAGE_SHOW
    char *pcWriteBuffer = malloc(1024);
    vTaskList(pcWriteBuffer);
    ESP_LOGI(TAG, "Task list: %s\n", pcWriteBuffer);
    free(pcWriteBuffer);
#endif
    SkOsShowMemUsage();
    SkOsGetTaskNames();
    esp_pm_dump_locks(stdout);
    return;
}

void SkOsShowCpuInfo() {
    int cpu_freq = esp_clk_cpu_freq();

    ESP_LOGI(TAG, "CPU Frequency: %d Hz", cpu_freq);
    return;
}

void* SkOsAllocPsram(size_t size, uint32_t align) {
    if (align == 0) {
        return heap_caps_malloc(size, MALLOC_CAP_SPIRAM);
    } else {
        return heap_caps_aligned_alloc(align, size, MALLOC_CAP_SPIRAM);
    }
}

void SkOsShowArray(uint8_t *data, uint32_t len) {
    uint8_t array[8];
    if (data == NULL || len == 0) {
        return;
    }

    // 每行显示8字节
    for (uint32_t i = 0; i < len; i += 8) {
        if (i + 8 < len) {
            ESP_LOGI(TAG, "data[%u]: %2x, %2x, %2x, %2x, %2x, %2x, %2x, %2x", i, 
                data[i], data[i + 1], data[i + 2], data[i + 3], 
                data[i + 4], data[i + 5], data[i + 6], data[i + 7]);
        } else {
            memcpy(array, data + i, len - i);
            ESP_LOGI(TAG, "data[%u]: %2x, %2x, %2x, %2x, %2x, %2x, %2x, %2x", i, 
                array[0], array[1], array[2], array[3], 
                array[4], array[5], array[6], array[7]);
        }
    }

    return;
}

void SkOsShowPointerArray(uint8_t *data, uint32_t len) {
    uint32_t array[4];
    uint32_t *p = (uint32_t *)data;
    uint32_t cnt = len / sizeof(uint32_t);

    if (data == NULL || len == 0) {
        return;
    }

    // 每行显示8字节
    for (uint32_t i = 0; i < cnt; i += 4) {
        if (i + 4 < cnt) {
            ESP_LOGI(TAG, "data[%u]: %p, %p, %p, %p", i, 
                p[i], p[i + 1], p[i + 2], p[i + 3]);
        } else {
            memcpy(array, p + i, (cnt - i)*sizeof(uint32_t));
            ESP_LOGI(TAG, "data[%u]: %2x, %2x, %2x, %2x", i, 
                array[0], array[1], array[2], array[3]);
        }
    }

    return;
}
