/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_opus_enc.h
 * @description: OPUS编码子模块
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#ifndef SK_OPUS_ENC_H
#define SK_OPUS_ENC_H
#include <stdint.h>
#include <freertos/queue.h>
#include "sk_audio_buffer.h"

#ifdef __cplusplus
extern "C" {
#endif

#define AUDIO_CODED_BUF_SIZE 380

typedef void* SkOpusEncHandler;

typedef void (*SkOpusEncCallback)(void *private, const uint8_t *data, size_t len, uint32_t timestamp);

enum {
    AUDIO_IDX_CALLREQ,
    AUDIO_IDX_HELLO,
    AUDIO_IDX_HELLOFIRST,
};

typedef struct {
    uint8_t flag;
    uint8_t vad;
    uint16_t frameSize;
    uint8_t data[AUDIO_CODED_BUF_SIZE - 4];
} SkCmdData;

SkOpusEncHandler SkOpusEncGetHandler();
SkOpusEncHandler SkOpusEncInit(int sampleRate, int channels, int durationMs, QueueHandle_t msgQueue);
void SkOpusEncDeinit(SkOpusEncHandler handler);
void SkOpusEncSetCallback(SkOpusEncCallback cb, void *arg);
int32_t SkOpusEncSendInner(uint8_t audioIndex);
void SkOpusEncEnqueue(uint16_t *buff, size_t len, uint32_t dataMode, uint32_t tickCnt);
void SkOpusEncShowStatus();
void SkOpusEncBypass(int flag);
void SkOpusEncStopTask();
void SkOpusEncRegAudio(char* name, const uint8_t* data, int length, const uint16_t *pktOffset, int16_t pktCnt);
void SkOpusEncPutAudioIntoQueue(SkOpusEncHandler handler, SkAudioBuf *inBuf);
void SkOpusEncProcAudioBuf(SkOpusEncHandler handler, SkAudioBuf *inBuf);
int32_t SkOpusEncSetLocal(SkOpusEncHandler handler, uint8_t audioIndex);
void SkOpusEncSetFlag(int flag);
SkCmdData* SkOpusPopCmdAudio();
bool SkOpusEncProc(SkOpusEncHandler handler);

#ifdef __cplusplus
}
#endif

#endif

