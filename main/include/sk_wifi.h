/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_wifi.h
 * @description: WIFI接口封装.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#ifndef SK_WIFI_H
#define SK_WIFI_H

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    int32_t event;
    int32_t subEvent;
} SkWifiEvent;

enum {
    SK_WIFI_EVENT_NULL = 0,
    SK_WIFI_EVENT_START_STA = 1,
    SK_WIFI_EVENT_START_AP = 2,
    SK_WIFI_EVENT_STOP = 3,
};

void SkWifiInit();
void SkWifiDeinit();
int32_t SkWifiStartAp();
int32_t SkWifiStartSta();
int32_t SkWifiStop();
void SkWifiGetIp(uint32_t wifiMode, char *ipStr, int32_t len);

#ifdef __cplusplus
}
#endif

#endif