/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: my_app.c
 * @description: 自定义应用程序 - 通过GPIO9按键控制陀螺仪数据读取
 * @author: User
 * @date: 2025-07-12
 */
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <math.h>
#include <inttypes.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <errno.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "freertos/timers.h"
#include "driver/gpio.h"
#include "esp_log.h"
#include "sk_board.h"
#include "sk_common.h"
#include "sk_os.h"
#include "sk_sm.h"

#define TAG "MyApp"

// 使用FT开发板的功能按键1 (GPIO42) - 大数据模式控制
#define BIG_DATA_KEY        1  // 对应GPIO_FUNC_KEY1 (GPIO42)
// 使用FT开发板的功能按键2 (GPIO41) - 小数据发送
#define SMALL_DATA_KEY      2  // 对应GPIO_FUNC_KEY2 (GPIO41)

// 事件位定义
#define BIG_DATA_START_BIT      BIT0
#define BIG_DATA_STOP_BIT       BIT1
#define SMALL_DATA_SEND_BIT     BIT2
#define TASK_EXIT_BIT           BIT3

// 数据数组配置
#define BIG_DATA_ARRAY_SIZE     1000      // 大数据数组大小
#define SMALL_DATA_ARRAY_SIZE   5       // 小数据数组大小
#define BIG_DATA_SEND_INTERVAL_MS   100 // 大数据发送间隔(ms)
#define DATA_COLLECT_INTERVAL_MS    100 // 数据收集间隔(ms)

// TCP服务器配置
#define TCP_SERVER_IP           "************"  // 服务器IP地址
#define TCP_SERVER_PORT         8080            // 服务器端口
#define TCP_RECONNECT_DELAY_MS  5000           // 重连延时(ms)
#define TCP_SEND_TIMEOUT_MS     1000           // 发送超时(ms)

// 数据样本结构
typedef struct {
    SkSensorData_t sensorData;
    SkTHSData_t thsData;
    int32_t batteryRaw;
    uint32_t timestamp;
    bool thsValid;
} DataSample_t;

// 大数据数组结构
typedef struct {
    DataSample_t samples[BIG_DATA_ARRAY_SIZE];
    uint32_t writeIndex;
    uint32_t readIndex;
    uint32_t count;
    bool isFull;
    bool isCollecting;
    bool isSending;
} BigDataArray_t;

// 小数据数组结构
typedef struct {
    DataSample_t samples[SMALL_DATA_ARRAY_SIZE];
    uint32_t count;
} SmallDataArray_t;

typedef struct {
    EventGroupHandle_t eventGroup;
    TaskHandle_t dataTaskHandle;
    TaskHandle_t keyMonitorTaskHandle;
    TaskHandle_t tcpTaskHandle;
    TaskHandle_t bigDataSendTaskHandle;
    bool isRunning;
    uint32_t readCount;
    uint32_t successCount;
    uint32_t failCount;
    int32_t lastKeyState;
    uint32_t startTime;

    // TCP连接相关
    int tcpSocket;
    bool tcpConnected;
    uint32_t tcpSendCount;
    uint32_t tcpSendFailCount;

    // 数据数组
    BigDataArray_t bigDataArray;
    SmallDataArray_t smallDataArray;

    // 模式状态
    bool bigDataMode;
    bool smallDataMode;
} MyAppCtrl_t;

static MyAppCtrl_t g_myAppCtrl = {0};

/**
 * @brief TCP连接函数
 */
static int tcp_connect(void)
{
    int sock;
    struct sockaddr_in server_addr;
    struct timeval timeout;

    // 创建socket
    sock = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (sock < 0) {
        ESP_LOGE(TAG, "Failed to create socket: errno %d", errno);
        return -1;
    }

    // 设置发送超时
    timeout.tv_sec = TCP_SEND_TIMEOUT_MS / 1000;
    timeout.tv_usec = (TCP_SEND_TIMEOUT_MS % 1000) * 1000;
    if (setsockopt(sock, SOL_SOCKET, SO_SNDTIMEO, &timeout, sizeof(timeout)) < 0) {
        ESP_LOGW(TAG, "Failed to set send timeout");
    }

    // 设置接收超时
    if (setsockopt(sock, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout)) < 0) {
        ESP_LOGW(TAG, "Failed to set recv timeout");
    }

    // 配置服务器地址
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(TCP_SERVER_PORT);
    if (inet_pton(AF_INET, TCP_SERVER_IP, &server_addr.sin_addr) <= 0) {
        ESP_LOGE(TAG, "Invalid server IP address: %s", TCP_SERVER_IP);
        close(sock);
        return -1;
    }

    // 连接服务器
    if (connect(sock, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
        ESP_LOGE(TAG, "Failed to connect to %s:%d, errno %d", TCP_SERVER_IP, TCP_SERVER_PORT, errno);
        close(sock);
        return -1;
    }

    ESP_LOGI(TAG, "Successfully connected to TCP server %s:%d", TCP_SERVER_IP, TCP_SERVER_PORT);
    return sock;
}

/**
 * @brief TCP断开连接函数
 */
static void tcp_disconnect(int sock)
{
    if (sock >= 0) {
        close(sock);
        ESP_LOGI(TAG, "TCP connection closed");
    }
}

/**
 * @brief 发送数据到TCP服务器
 */
static int tcp_send_data(int sock, const char* data, size_t len)
{
    if (sock < 0 || data == NULL || len == 0) {
        return -1;
    }

    int sent = send(sock, data, len, 0);
    if (sent < 0) {
        ESP_LOGE(TAG, "Failed to send data: errno %d", errno);
        return -1;
    } else if (sent != len) {
        ESP_LOGW(TAG, "Partial send: %d/%d bytes", sent, len);
        return sent;
    }

    return sent;
}

/**
 * @brief 按键监控任务
 */
static void key_monitor_task(void* pvParameters)
{
    MyAppCtrl_t* ctrl = (MyAppCtrl_t*)pvParameters;
    int32_t currentKeyState;
    int32_t lastKeyState = 0;  // 使用局部变量减少结构体访问

    ESP_LOGI(TAG, "Key monitor task started with 4KB stack");

    while (1) {
        // 使用工程内置的按键读取函数
        currentKeyState = SkBspGetFuncKey();

        // 检测按键按下事件（从0变为非0）
        if (lastKeyState == 0 && currentKeyState != 0) {
            ESP_LOGI(TAG, "Key pressed: %d", currentKeyState);

            switch (currentKeyState) {
                case BIG_DATA_KEY:
                    // 大数据模式控制按键
                    if (ctrl->bigDataMode) {
                        // 当前正在大数据模式，发送停止信号
                        xEventGroupSetBits(ctrl->eventGroup, BIG_DATA_STOP_BIT);
                        ESP_LOGI(TAG, "Stopping big data mode...");
                    } else {
                        // 当前未运行，发送启动信号
                        xEventGroupSetBits(ctrl->eventGroup, BIG_DATA_START_BIT);
                        ESP_LOGI(TAG, "Starting big data mode...");
                    }
                    break;

                case SMALL_DATA_KEY:
                    // 小数据发送按键
                    ESP_LOGI(TAG, "Small data send triggered by key press!");
                    xEventGroupSetBits(ctrl->eventGroup, SMALL_DATA_SEND_BIT);
                    break;

                default:
                    ESP_LOGW(TAG, "Unknown key pressed: %d", currentKeyState);
                    break;
            }
        }

        lastKeyState = currentKeyState;
        ctrl->lastKeyState = currentKeyState;  // 同步到结构体
        vTaskDelay(pdMS_TO_TICKS(100)); // 100ms检测间隔
    }
}

/**
 * @brief 收集数据样本
 */
static bool collect_data_sample(DataSample_t* sample)
{
    // 读取陀螺仪数据
    if (SkGyroReadData(&sample->sensorData) != SK_RET_SUCCESS) {
        return false;
    }

    // 读取温湿度数据
    sample->thsValid = (SkTHSReadData(&sample->thsData) == SK_RET_SUCCESS);

    // 读取电池数据
    sample->batteryRaw = SkBspGetBattery();

    // 记录时间戳
    sample->timestamp = xTaskGetTickCount();

    return true;
}

/**
 * @brief 添加数据到大数据数组
 */
static void add_to_big_data_array(BigDataArray_t* array, const DataSample_t* sample)
{
    array->samples[array->writeIndex] = *sample;
    array->writeIndex = (array->writeIndex + 1) % BIG_DATA_ARRAY_SIZE;

    if (array->count < BIG_DATA_ARRAY_SIZE) {
        array->count++;
    } else {
        array->isFull = true;
        // 如果数组满了，移动读取索引
        array->readIndex = (array->readIndex + 1) % BIG_DATA_ARRAY_SIZE;
    }
}

/**
 * @brief 添加数据到小数据数组
 */
static void add_to_small_data_array(SmallDataArray_t* array, const DataSample_t* sample)
{
    if (array->count < SMALL_DATA_ARRAY_SIZE) {
        array->samples[array->count] = *sample;
        array->count++;
    } else {
        // 数组满了，移动数据，添加新数据到末尾
        for (int i = 0; i < SMALL_DATA_ARRAY_SIZE - 1; i++) {
            array->samples[i] = array->samples[i + 1];
        }
        array->samples[SMALL_DATA_ARRAY_SIZE - 1] = *sample;
    }
}

/**
 * @brief 格式化数据样本为字符串
 */
static int format_data_sample(const DataSample_t* sample, char* buffer, size_t bufferSize,
                             uint32_t index, const char* prefix)
{
    // 转换系数
    float accelScale = 2.0f / 32768.0f;    // ±2g量程
    float gyroScale = 250.0f / 32768.0f;   // ±250dps量程
    float tempScale = 1.0f / 128.0f;       // 温度转换系数

    // 转换为物理量
    float accelX = (float)sample->sensorData.accel[0] * accelScale;
    float accelY = (float)sample->sensorData.accel[1] * accelScale;
    float accelZ = (float)sample->sensorData.accel[2] * accelScale;
    float accelTotal = sqrt(accelX * accelX + accelY * accelY + accelZ * accelZ) - 1.0f;

    float gyroX = (float)sample->sensorData.gyro[0] * gyroScale;
    float gyroY = (float)sample->sensorData.gyro[1] * gyroScale;
    float gyroZ = (float)sample->sensorData.gyro[2] * gyroScale;

    float temperature = (float)sample->sensorData.temp * tempScale + 25.0f;

    // 格式化字符串
    int len;
    if (sample->thsValid) {
        len = snprintf(buffer, bufferSize,
            "%s:SkBsp: BAT Raw Data: %" PRId32 "\n%s: Sample #%" PRIu32 ": Accel[%.3f,%.3f,%.3f]g Total=%.3fg Gyro[%.2f,%.2f,%.2f]°/s Temp=%.1f°C Steps=%d THS_Temp=%.1f°C THS_Humi=%.1f%%\n",
            prefix, sample->batteryRaw, prefix, index, accelX, accelY, accelZ, accelTotal,
            gyroX, gyroY, gyroZ, temperature, sample->sensorData.step,
            sample->thsData.temp / 10.0f, sample->thsData.humi / 10.0f);
    } else {
        len = snprintf(buffer, bufferSize,
            "%s:SkBsp: BAT Raw Data: %" PRId32 "\n%s: Sample #%" PRIu32 ": Accel[%.3f,%.3f,%.3f]g Total=%.3fg Gyro[%.2f,%.2f,%.2f]°/s Temp=%.1f°C Steps=%d THS_Temp=N/A THS_Humi=N/A\n",
            prefix, sample->batteryRaw, prefix, index, accelX, accelY, accelZ, accelTotal,
            gyroX, gyroY, gyroZ, temperature, sample->sensorData.step);
    }

    return len;
}

/**
 * @brief TCP连接管理任务
 */
static void tcp_connection_task(void* pvParameters)
{
    MyAppCtrl_t* ctrl = (MyAppCtrl_t*)pvParameters;

    ESP_LOGI(TAG, "TCP connection task started");

    while (1) {
        // 检查是否需要退出
        EventBits_t bits = xEventGroupWaitBits(ctrl->eventGroup, TASK_EXIT_BIT,
                                               pdFALSE, pdFALSE, 0);
        if (bits & TASK_EXIT_BIT) {
            ESP_LOGI(TAG, "TCP task exit requested");
            break;
        }

        if (!ctrl->tcpConnected) {
            ESP_LOGI(TAG, "Attempting to connect to TCP server %s:%d", TCP_SERVER_IP, TCP_SERVER_PORT);

            ctrl->tcpSocket = tcp_connect();
            if (ctrl->tcpSocket >= 0) {
                ctrl->tcpConnected = true;
                ESP_LOGI(TAG, "TCP connection established");
            } else {
                ESP_LOGW(TAG, "TCP connection failed, retrying in %d ms", TCP_RECONNECT_DELAY_MS);
                vTaskDelay(pdMS_TO_TICKS(TCP_RECONNECT_DELAY_MS));
                continue;
            }
        }

        // // 连接已建立，检查连接状态
        // if (ctrl->tcpConnected) {
        //     // 发送心跳包或检查连接状态
        //     char heartbeat[] = "";
        //     if (tcp_send_data(ctrl->tcpSocket, heartbeat, strlen(heartbeat)) < 0) {
        //         ESP_LOGW(TAG, "TCP connection lost, attempting to reconnect");
        //         tcp_disconnect(ctrl->tcpSocket);
        //         ctrl->tcpSocket = -1;
        //         ctrl->tcpConnected = false;
        //     }
        // }

        // 每5秒检查一次连接状态
        vTaskDelay(pdMS_TO_TICKS(5000));
    }

    // 清理TCP连接
    if (ctrl->tcpConnected) {
        tcp_disconnect(ctrl->tcpSocket);
        ctrl->tcpSocket = -1;
        ctrl->tcpConnected = false;
    }

    ESP_LOGI(TAG, "TCP connection task ended");
    vTaskDelete(NULL);
}

/**
 * @brief 大数据发送任务
 */
static void big_data_send_task(void* pvParameters)
{
    MyAppCtrl_t* ctrl = (MyAppCtrl_t*)pvParameters;
    char sendBuffer[512];
    uint32_t sendIndex = 0;

    ESP_LOGI(TAG, "Big data send task started");

    while (1) {
        if (!ctrl->bigDataArray.isSending) {
            vTaskDelay(pdMS_TO_TICKS(100));
            continue;
        }

        BigDataArray_t* array = &ctrl->bigDataArray;

        // 检查是否有数据要发送
        if (array->count == 0) {
            vTaskDelay(pdMS_TO_TICKS(100));
            continue;
        }

        // 发送当前读取位置的数据
        const DataSample_t* sample = &array->samples[array->readIndex];
        int len = format_data_sample(sample, sendBuffer, sizeof(sendBuffer),
                                   sendIndex++, "BigData");

        // 转换传感器数据为物理量并打印到控制台
        float accelScale = 2.0f / 32768.0f;    // ±2g量程
        float gyroScale = 250.0f / 32768.0f;   // ±250dps量程
        float tempScale = 1.0f / 128.0f;       // 温度转换系数

        float accelX = (float)sample->sensorData.accel[0] * accelScale;
        float accelY = (float)sample->sensorData.accel[1] * accelScale;
        float accelZ = (float)sample->sensorData.accel[2] * accelScale;
        float accelTotal = sqrt(accelX * accelX + accelY * accelY + accelZ * accelZ) - 1.0f;

        float gyroX = (float)sample->sensorData.gyro[0] * gyroScale;
        float gyroY = (float)sample->sensorData.gyro[1] * gyroScale;
        float gyroZ = (float)sample->sensorData.gyro[2] * gyroScale;

        float temperature = (float)sample->sensorData.temp * tempScale + 25.0f;

        // 打印详细的传感器数据到控制台
        if (sample->thsValid) {
            ESP_LOGI(TAG, "BigData[%d/%d]: Sample #%d - Accel[%.3f,%.3f,%.3f]g Total=%.3fg Gyro[%.2f,%.2f,%.2f]°/s Temp=%.1f°C Steps=%d THS_Temp=%.1f°C THS_Humi=%.1f%% BAT=%d",
                     array->readIndex + 1, array->count, sendIndex - 1,
                     accelX, accelY, accelZ, accelTotal,
                     gyroX, gyroY, gyroZ, temperature, sample->sensorData.step,
                     sample->thsData.temp / 10.0f, sample->thsData.humi / 10.0f,
                     sample->batteryRaw);
        } else {
            ESP_LOGI(TAG, "BigData[%d/%d]: Sample #%d - Accel[%.3f,%.3f,%.3f]g Total=%.3fg Gyro[%.2f,%.2f,%.2f]°/s Temp=%.1f°C Steps=%d THS_Temp=N/A THS_Humi=N/A BAT=%d",
                     array->readIndex + 1, array->count, sendIndex - 1,
                     accelX, accelY, accelZ, accelTotal,
                     gyroX, gyroY, gyroZ, temperature, sample->sensorData.step,
                     sample->batteryRaw);
        }

        // 发送到TCP服务器
        if (ctrl->tcpConnected && ctrl->tcpSocket >= 0) {
            if (tcp_send_data(ctrl->tcpSocket, sendBuffer, len) > 0) {
                ctrl->tcpSendCount++;
            } else {
                ctrl->tcpSendFailCount++;
                ESP_LOGW(TAG, "Failed to send big data to TCP server");
            }
        }

        // 移动到下一个数据
        array->readIndex = (array->readIndex + 1) % BIG_DATA_ARRAY_SIZE;
        if (array->readIndex == array->writeIndex) {
            // 已经发送完所有数据，重新开始
            sendIndex = 0;
            ESP_LOGI(TAG, "Big data array sent completely, restarting...");
        }

        // 发送间隔
        vTaskDelay(pdMS_TO_TICKS(BIG_DATA_SEND_INTERVAL_MS));
    }

    ESP_LOGI(TAG, "Big data send task ended");
    vTaskDelete(NULL);
}

/**
 * @brief 发送小数据数组
 */
static void send_small_data_array(MyAppCtrl_t* ctrl)
{
    char sendBuffer[512];
    SmallDataArray_t* array = &ctrl->smallDataArray;

    ESP_LOGI(TAG, "Sending small data array (%d samples)", array->count);

    for (uint32_t i = 0; i < array->count; i++) {
        const DataSample_t* sample = &array->samples[i];
        int len = format_data_sample(sample, sendBuffer, sizeof(sendBuffer),
                                   i, "SmallData");

        // 转换传感器数据为物理量并打印到控制台
        float accelScale = 2.0f / 32768.0f;    // ±2g量程
        float gyroScale = 250.0f / 32768.0f;   // ±250dps量程
        float tempScale = 1.0f / 128.0f;       // 温度转换系数

        float accelX = (float)sample->sensorData.accel[0] * accelScale;
        float accelY = (float)sample->sensorData.accel[1] * accelScale;
        float accelZ = (float)sample->sensorData.accel[2] * accelScale;
        float accelTotal = sqrt(accelX * accelX + accelY * accelY + accelZ * accelZ) - 1.0f;

        float gyroX = (float)sample->sensorData.gyro[0] * gyroScale;
        float gyroY = (float)sample->sensorData.gyro[1] * gyroScale;
        float gyroZ = (float)sample->sensorData.gyro[2] * gyroScale;

        float temperature = (float)sample->sensorData.temp * tempScale + 25.0f;

        // 打印详细的传感器数据到控制台
        if (sample->thsValid) {
            ESP_LOGI(TAG, "SmallData[%d/%d]: Sample #%d - Accel[%.3f,%.3f,%.3f]g Total=%.3fg Gyro[%.2f,%.2f,%.2f]°/s Temp=%.1f°C Steps=%d THS_Temp=%.1f°C THS_Humi=%.1f%% BAT=%d",
                     i + 1, array->count, i,
                     accelX, accelY, accelZ, accelTotal,
                     gyroX, gyroY, gyroZ, temperature, sample->sensorData.step,
                     sample->thsData.temp / 10.0f, sample->thsData.humi / 10.0f,
                     sample->batteryRaw);
        } else {
            ESP_LOGI(TAG, "SmallData[%d/%d]: Sample #%d - Accel[%.3f,%.3f,%.3f]g Total=%.3fg Gyro[%.2f,%.2f,%.2f]°/s Temp=%.1f°C Steps=%d THS_Temp=N/A THS_Humi=N/A BAT=%d",
                     i + 1, array->count, i,
                     accelX, accelY, accelZ, accelTotal,
                     gyroX, gyroY, gyroZ, temperature, sample->sensorData.step,
                     sample->batteryRaw);
        }

        // 发送到TCP服务器
        if (ctrl->tcpConnected && ctrl->tcpSocket >= 0) {
            if (tcp_send_data(ctrl->tcpSocket, sendBuffer, len) > 0) {
                ctrl->tcpSendCount++;
            } else {
                ctrl->tcpSendFailCount++;
                ESP_LOGW(TAG, "Failed to send small data to TCP server");
            }
        }

        // 发送间隔
        vTaskDelay(pdMS_TO_TICKS(100));
    }

    ESP_LOGI(TAG, "Small data array sent completely");
}

/**
 * @brief 数据处理任务
 */
static void data_process_task(void* pvParameters)
{
    MyAppCtrl_t* ctrl = (MyAppCtrl_t*)pvParameters;
    DataSample_t sample;
    EventBits_t bits;

    ESP_LOGI(TAG, "Data process task started");

    while (1) {
        // 等待事件信号
        bits = xEventGroupWaitBits(ctrl->eventGroup,
                                   BIG_DATA_START_BIT | BIG_DATA_STOP_BIT |
                                   SMALL_DATA_SEND_BIT | TASK_EXIT_BIT,
                                   pdTRUE, pdFALSE, portMAX_DELAY);

        if (bits & TASK_EXIT_BIT) {
            ESP_LOGI(TAG, "Data process task exit requested");
            break;
        }

        if (bits & BIG_DATA_START_BIT) {
            ESP_LOGI(TAG, "=== Starting Big Data Mode ===");
            ESP_LOGI(TAG, "Press FUNC_KEY1 again to stop big data mode");

            // 初始化大数据模式
            ctrl->bigDataMode = true;
            ctrl->bigDataArray.writeIndex = 0;
            ctrl->bigDataArray.readIndex = 0;
            ctrl->bigDataArray.count = 0;
            ctrl->bigDataArray.isFull = false;
            ctrl->bigDataArray.isCollecting = true;
            ctrl->bigDataArray.isSending = false;

            ctrl->readCount = 0;
            ctrl->successCount = 0;
            ctrl->failCount = 0;

            // 创建并启动TCP连接管理任务
            if (ctrl->tcpTaskHandle == NULL) {
                BaseType_t ret = xTaskCreate(tcp_connection_task, "TcpConnTask", 4096,
                                           ctrl, 3, &ctrl->tcpTaskHandle);
                if (ret == pdPASS) {
                    ESP_LOGI(TAG, "TCP connection task created and started");
                } else {
                    ESP_LOGW(TAG, "Failed to create TCP connection task");
                }
            }

            // 创建并启动大数据发送任务
            if (ctrl->bigDataSendTaskHandle == NULL) {
                BaseType_t ret = xTaskCreate(big_data_send_task, "BigDataSendTask", 4096,
                                           ctrl, 4, &ctrl->bigDataSendTaskHandle);
                if (ret == pdPASS) {
                    ESP_LOGI(TAG, "Big data send task created and started");
                } else {
                    ESP_LOGW(TAG, "Failed to create big data send task");
                }
            }

            // 持续收集数据到大数组，直到收到停止信号
            // 这是一个无限循环，会不断收集数据并发送，直到用户按键停止
            while (ctrl->bigDataMode) {
                ctrl->readCount++;

                // 收集数据样本
                if (collect_data_sample(&sample)) {
                    ctrl->successCount++;

                    // 添加到大数据数组（环形缓冲区，会自动覆盖旧数据）
                    add_to_big_data_array(&ctrl->bigDataArray, &sample);

                    ESP_LOGI(TAG, "BigData collected sample #%d [%d/%d] - Continuous mode",
                             ctrl->readCount, ctrl->bigDataArray.count, BIG_DATA_ARRAY_SIZE);

                    // 如果数组满了，开始发送（如果还没开始的话）
                    if ((ctrl->bigDataArray.count >= BIG_DATA_ARRAY_SIZE || ctrl->bigDataArray.isFull)
                        && !ctrl->bigDataArray.isSending) {
                        ESP_LOGI(TAG, "Big data array is full, starting continuous send mode...");
                        ctrl->bigDataArray.isSending = true;
                    }
                } else {
                    ctrl->failCount++;
                    ESP_LOGW(TAG, "Failed to collect data sample #%d", ctrl->readCount);
                }

                // 检查是否有停止信号（非阻塞检查）
                bits = xEventGroupWaitBits(ctrl->eventGroup, BIG_DATA_STOP_BIT,
                                           pdTRUE, pdFALSE, 0);
                if (bits & BIG_DATA_STOP_BIT) {
                    ESP_LOGI(TAG, "Big data continuous mode stopped by user at sample #%d", ctrl->readCount);
                    // 立即停止大数据模式
                    ctrl->bigDataMode = false;
                    ctrl->bigDataArray.isCollecting = false;
                    ctrl->bigDataArray.isSending = false;
                    break;
                }

                // 数据收集间隔
                vTaskDelay(pdMS_TO_TICKS(DATA_COLLECT_INTERVAL_MS));
            }

            ESP_LOGI(TAG, "Big data collection loop ended");
        }

        if (bits & BIG_DATA_STOP_BIT) {
            ESP_LOGI(TAG, "=== Stopping Big Data Mode ===");

            // 停止大数据模式（如果还在运行的话）
            if (ctrl->bigDataMode) {
                ctrl->bigDataMode = false;
                ctrl->bigDataArray.isCollecting = false;
                ctrl->bigDataArray.isSending = false;

                // 清空大数据数组
                ctrl->bigDataArray.writeIndex = 0;
                ctrl->bigDataArray.readIndex = 0;
                ctrl->bigDataArray.count = 0;
                ctrl->bigDataArray.isFull = false;

                ESP_LOGI(TAG, "Big data mode stopped and array cleared");
            } else {
                ESP_LOGI(TAG, "Big data mode was already stopped");
            }
        }

        if (bits & SMALL_DATA_SEND_BIT) {
            ESP_LOGI(TAG, "=== Small Data Send Triggered ===");

            // 收集小数据数组
            ctrl->smallDataArray.count = 0;

            for (int i = 0; i < SMALL_DATA_ARRAY_SIZE; i++) {
                if (collect_data_sample(&sample)) {
                    add_to_small_data_array(&ctrl->smallDataArray, &sample);
                    ESP_LOGI(TAG, "SmallData collected sample %d/%d", i + 1, SMALL_DATA_ARRAY_SIZE);
                } else {
                    ESP_LOGW(TAG, "Failed to collect small data sample %d", i + 1);
                }
                vTaskDelay(pdMS_TO_TICKS(200)); // 200ms间隔收集
            }

            // 发送小数据数组
            send_small_data_array(ctrl);
        }
    }

    // 任务结束清理
    ESP_LOGI(TAG, "Data process task ending, cleaning up...");

    // 停止所有模式
    ctrl->bigDataMode = false;
    ctrl->smallDataMode = false;
    ctrl->bigDataArray.isCollecting = false;
    ctrl->bigDataArray.isSending = false;

    // 断开TCP连接并停止TCP任务
    if (ctrl->tcpConnected) {
        tcp_disconnect(ctrl->tcpSocket);
        ctrl->tcpSocket = -1;
        ctrl->tcpConnected = false;
        ESP_LOGI(TAG, "TCP connection closed");
    }

    // 停止TCP任务
    if (ctrl->tcpTaskHandle) {
        // 发送退出信号给TCP任务
        xEventGroupSetBits(ctrl->eventGroup, TASK_EXIT_BIT);
        vTaskDelay(pdMS_TO_TICKS(100)); // 等待任务退出
        ctrl->tcpTaskHandle = NULL;
        ESP_LOGI(TAG, "TCP task stopped");
    }

    // 停止大数据发送任务
    if (ctrl->bigDataSendTaskHandle) {
        vTaskDelay(pdMS_TO_TICKS(100)); // 等待任务退出
        ctrl->bigDataSendTaskHandle = NULL;
        ESP_LOGI(TAG, "Big data send task stopped");
    }

    // 打印统计结果
    ESP_LOGI(TAG, "=== Data Process Results ===");
    ESP_LOGI(TAG, "Total samples: %d", ctrl->readCount);
    ESP_LOGI(TAG, "Success: %d", ctrl->successCount);
    ESP_LOGI(TAG, "Failed: %d", ctrl->failCount);
    ESP_LOGI(TAG, "TCP sent: %d", ctrl->tcpSendCount);
    ESP_LOGI(TAG, "TCP failed: %d", ctrl->tcpSendFailCount);
    ESP_LOGI(TAG, "TCP connected: %s", ctrl->tcpConnected ? "Yes" : "No");

    if (ctrl->readCount > 0) {
        ESP_LOGI(TAG, "Success rate: %.2f%%",
                 (float)ctrl->successCount / ctrl->readCount * 100.0f);
        if (ctrl->tcpSendCount > 0) {
            ESP_LOGI(TAG, "TCP send rate: %.2f%%",
                     (float)ctrl->tcpSendCount / ctrl->successCount * 100.0f);
        }
    }
    ESP_LOGI(TAG, "=== Data Process Session Completed ===");

    ESP_LOGI(TAG, "Data process task ended");
    vTaskDelete(NULL);
}

/**
 * @brief 初始化我的应用程序
 */
void MyAppInit(void)
{
    MyAppCtrl_t* ctrl = &g_myAppCtrl;
    
    ESP_LOGI(TAG, "Initializing My App...");
    
    // 创建事件组
    ctrl->eventGroup = xEventGroupCreate();
    if (ctrl->eventGroup == NULL) {
        ESP_LOGE(TAG, "Failed to create event group");
        return;
    }
    
    // 创建按键监控任务 - 增加栈大小以避免栈溢出
    BaseType_t ret = xTaskCreate(key_monitor_task, "KeyMonitorTask", 4096,
                                 ctrl, 4, &ctrl->keyMonitorTaskHandle);
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create key monitor task");
        return;
    }

    // 创建数据处理任务
    ret = xTaskCreate(data_process_task, "DataProcessTask", 8192,
                      ctrl, 5, &ctrl->dataTaskHandle);
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create data process task");
        return;
    }

    // TCP任务和大数据发送任务将在需要时创建

    // 初始化TCP相关变量
    ctrl->tcpSocket = -1;
    ctrl->tcpConnected = false;
    ctrl->tcpSendCount = 0;
    ctrl->tcpSendFailCount = 0;

    // 初始化数据数组
    memset(&ctrl->bigDataArray, 0, sizeof(BigDataArray_t));
    memset(&ctrl->smallDataArray, 0, sizeof(SmallDataArray_t));

    // 初始化模式状态
    ctrl->bigDataMode = false;
    ctrl->smallDataMode = false;

    ESP_LOGI(TAG, "My App initialized successfully");
}

/**
 * @brief 反初始化我的应用程序
 */
void MyAppDeinit(void)
{
    MyAppCtrl_t* ctrl = &g_myAppCtrl;
    
    ESP_LOGI(TAG, "Deinitializing My App...");
    
    // 发送退出信号
    if (ctrl->eventGroup) {
        xEventGroupSetBits(ctrl->eventGroup, TASK_EXIT_BIT);
    }

    // 等待任务结束
    if (ctrl->dataTaskHandle) {
        vTaskDelay(pdMS_TO_TICKS(100));
        ctrl->dataTaskHandle = NULL;
    }

    if (ctrl->tcpTaskHandle) {
        vTaskDelay(pdMS_TO_TICKS(100));
        ctrl->tcpTaskHandle = NULL;
    }

    if (ctrl->bigDataSendTaskHandle) {
        vTaskDelay(pdMS_TO_TICKS(100));
        ctrl->bigDataSendTaskHandle = NULL;
    }

    if (ctrl->keyMonitorTaskHandle) {
        vTaskDelete(ctrl->keyMonitorTaskHandle);
        ctrl->keyMonitorTaskHandle = NULL;
    }

    // 清理TCP连接
    if (ctrl->tcpConnected) {
        tcp_disconnect(ctrl->tcpSocket);
        ctrl->tcpSocket = -1;
        ctrl->tcpConnected = false;
    }

    // 删除事件组
    if (ctrl->eventGroup) {
        vEventGroupDelete(ctrl->eventGroup);
        ctrl->eventGroup = NULL;
    }

    ESP_LOGI(TAG, "My App deinitialized");
}

/**
 * @brief 获取当前读取状态
 */
void MyAppGetStatus(void)
{
    MyAppCtrl_t* ctrl = &g_myAppCtrl;

    ESP_LOGI(TAG, "=== Current Status ===");
    ESP_LOGI(TAG, "Big Data Mode: %s", ctrl->bigDataMode ? "Yes" : "No");
    ESP_LOGI(TAG, "Small Data Mode: %s", ctrl->smallDataMode ? "Yes" : "No");
    ESP_LOGI(TAG, "Big Data Collecting: %s", ctrl->bigDataArray.isCollecting ? "Yes" : "No");
    ESP_LOGI(TAG, "Big Data Sending: %s", ctrl->bigDataArray.isSending ? "Yes" : "No");
    ESP_LOGI(TAG, "Big Data Count: %d/%d", ctrl->bigDataArray.count, BIG_DATA_ARRAY_SIZE);
    ESP_LOGI(TAG, "Small Data Count: %d/%d", ctrl->smallDataArray.count, SMALL_DATA_ARRAY_SIZE);
    ESP_LOGI(TAG, "Read count: %d", ctrl->readCount);
    ESP_LOGI(TAG, "Success: %d", ctrl->successCount);
    ESP_LOGI(TAG, "Failed: %d", ctrl->failCount);
    ESP_LOGI(TAG, "TCP connected: %s", ctrl->tcpConnected ? "Yes" : "No");
    ESP_LOGI(TAG, "TCP sent: %d", ctrl->tcpSendCount);
    ESP_LOGI(TAG, "TCP failed: %d", ctrl->tcpSendFailCount);
    if (ctrl->readCount > 0) {
        ESP_LOGI(TAG, "Success rate: %.2f%%",
                 (float)ctrl->successCount / ctrl->readCount * 100.0f);
        if (ctrl->tcpSendCount > 0) {
            ESP_LOGI(TAG, "TCP send rate: %.2f%%",
                     (float)ctrl->tcpSendCount / ctrl->successCount * 100.0f);
        }
    }
}