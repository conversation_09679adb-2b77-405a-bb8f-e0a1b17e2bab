# 按键功能重构说明

## 修改概述

已成功重构按键功能，删除OTA升级代码，实现大数据和小数据陀螺仪发送功能。

## 主要修改内容

### 1. 按键功能重新定义

#### 原来的按键功能：
- **FUNC_KEY1 (GPIO42)**: 陀螺仪数据读取开始/停止
- **FUNC_KEY2 (GPIO41)**: OTA升级触发

#### 修改后的按键功能：
- **FUNC_KEY1 (GPIO42)**: 大数据模式控制（按下开始收集大数据并循环发送，再次按下停止）
- **FUNC_KEY2 (GPIO41)**: 小数据发送（按下时立即收集并发送5个陀螺仪数据样本）

### 2. 数据结构设计

#### 新增数据结构：
```c
// 数据样本结构
typedef struct {
    SkSensorData_t sensorData;  // 陀螺仪数据
    SkTHSData_t thsData;        // 温湿度数据
    int32_t batteryRaw;         // 电池数据
    uint32_t timestamp;         // 时间戳
    bool thsValid;              // 温湿度数据有效性
} DataSample_t;

// 大数据数组结构（50个样本）
typedef struct {
    DataSample_t samples[BIG_DATA_ARRAY_SIZE];
    uint32_t writeIndex;        // 写入索引
    uint32_t readIndex;         // 读取索引
    uint32_t count;             // 数据数量
    bool isFull;                // 数组是否满
    bool isCollecting;          // 是否正在收集
    bool isSending;             // 是否正在发送
} BigDataArray_t;

// 小数据数组结构（5个样本）
typedef struct {
    DataSample_t samples[SMALL_DATA_ARRAY_SIZE];
    uint32_t count;             // 数据数量
} SmallDataArray_t;
```

### 3. 任务架构重构

#### 原来的任务：
- `gyro_read_task`: 陀螺仪数据读取任务
- `key_monitor_task`: 按键监控任务
- `tcp_connection_task`: TCP连接管理任务

#### 修改后的任务：
- `data_process_task`: 数据处理任务（替代原来的gyro_read_task）
- `big_data_send_task`: 大数据发送任务（新增）
- `key_monitor_task`: 按键监控任务（功能修改）
- `tcp_connection_task`: TCP连接管理任务（保持不变）

### 4. 事件位重新定义

#### 原来的事件位：
```c
#define GYRO_TASK_START_BIT     BIT0
#define GYRO_TASK_STOP_BIT      BIT1
#define GYRO_TASK_EXIT_BIT      BIT2
#define GYRO_RESET_BIT          BIT3
```

#### 修改后的事件位：
```c
#define BIG_DATA_START_BIT      BIT0
#define BIG_DATA_STOP_BIT       BIT1
#define SMALL_DATA_SEND_BIT     BIT2
#define TASK_EXIT_BIT           BIT3
```

### 5. 核心功能实现

#### 大数据模式工作流程：
1. **按下FUNC_KEY1第一次**：
   - 开始收集陀螺仪数据到大数组（1000个样本）
   - 每100ms收集一个样本
   - 数组满后开始发送，同时继续收集新数据
   - 每100ms发送一个样本到TCP服务器
   - **持续循环**：收集→发送→再收集→再发送，无限循环

2. **按下FUNC_KEY1第二次**：
   - 立即停止数据收集和发送
   - 清空大数据数组

#### 小数据模式工作流程：
1. **按下FUNC_KEY2**：
   - 立即收集5个陀螺仪数据样本
   - 每200ms收集一个样本
   - 收集完成后立即发送到TCP服务器
   - 每100ms发送一个样本

### 6. 数据格式

#### 大数据格式：
```
BigData:SkBsp: BAT Raw Data: 2048
BigData: Sample #1: Accel[0.123,0.456,0.789]g Total=0.234g Gyro[1.23,4.56,7.89]°/s Temp=25.5°C Steps=100 THS_Temp=24.6°C THS_Humi=65.4%
```

#### 小数据格式：
```
SmallData:SkBsp: BAT Raw Data: 2048
SmallData: Sample #1: Accel[0.123,0.456,0.789]g Total=0.234g Gyro[1.23,4.56,7.89]°/s Temp=25.5°C Steps=100 THS_Temp=24.6°C THS_Humi=65.4%
```

### 7. 删除的功能

#### 完全删除的代码：
- **OTA升级相关代码**：`SkSmTriggerOta()` 调用
- **陀螺仪复位定时器**：`gyro_reset_timer_callback()` 函数
- **定时器相关代码**：创建、启动、停止定时器的代码
- **旧的数据处理函数**：`process_sensor_data()` 函数

### 8. 新增的辅助函数

```c
// 数据收集函数
bool collect_data_sample(DataSample_t* sample);

// 数组操作函数
void add_to_big_data_array(BigDataArray_t* array, const DataSample_t* sample);
void add_to_small_data_array(SmallDataArray_t* array, const DataSample_t* sample);

// 数据格式化函数
int format_data_sample(const DataSample_t* sample, char* buffer, size_t bufferSize, 
                      uint32_t index, const char* prefix);

// 小数据发送函数
void send_small_data_array(MyAppCtrl_t* ctrl);
```

## 配置参数

```c
#define BIG_DATA_ARRAY_SIZE     1000    // 大数据数组大小
#define SMALL_DATA_ARRAY_SIZE   5       // 小数据数组大小
#define BIG_DATA_SEND_INTERVAL_MS   100 // 大数据发送间隔(ms)
#define DATA_COLLECT_INTERVAL_MS    100 // 数据收集间隔(ms)
```

## 使用方法

### 大数据模式（持续循环）：
1. 按下 FUNC_KEY1 (GPIO42) 开始大数据模式
2. 系统开始收集陀螺仪数据到大数组（1000个样本）
3. 数组满后开始发送数据，同时继续收集新数据
4. **无限循环**：持续收集→发送→再收集→再发送
5. 再次按下 FUNC_KEY1 立即停止大数据模式

#### 持续循环特性：
- **不间断运行**：一旦启动，会持续运行直到手动停止
- **环形缓冲**：新数据会覆盖最旧的数据，保持1000个最新样本
- **并行处理**：收集和发送同时进行，互不干扰
- **实时更新**：发送的数据始终是最新收集的数据

### 小数据模式：
1. 按下 FUNC_KEY2 (GPIO41) 触发小数据发送
2. 系统立即收集5个陀螺仪数据样本
3. 收集完成后立即发送到TCP服务器

## 编译状态

✅ **编译成功**，无错误和警告
✅ **功能完整**，所有新功能已实现
✅ **代码优化**，删除了不必要的代码
✅ **内存优化**，合理设计数据结构大小

## 特性优势

1. **灵活的数据发送模式**：支持大数据循环发送和小数据快速发送
2. **网络容错性**：TCP断开不影响数据收集，重连后继续发送
3. **内存管理**：使用环形缓冲区优化内存使用
4. **实时监控**：控制台实时显示数据收集和发送状态
5. **错误处理**：完善的错误处理和日志记录
6. **温湿度集成**：包含温湿度传感器数据，显示1位小数精度

## 日志输出优化

### 详细传感器数据显示

现在控制台会显示详细的传感器数据，包括：

#### 大数据模式日志示例：
```
I (16:51:22.907) MyApp: BigData[20/50]: Sample #19 - Accel[0.123,0.456,0.789]g Total=0.234g Gyro[1.23,4.56,7.89]°/s Temp=25.5°C Steps=100 THS_Temp=24.6°C THS_Humi=65.4% BAT=2459
```

#### 小数据模式日志示例：
```
I (16:51:22.907) MyApp: SmallData[1/5]: Sample #0 - Accel[0.123,0.456,0.789]g Total=0.234g Gyro[1.23,4.56,7.89]°/s Temp=25.5°C Steps=100 THS_Temp=24.6°C THS_Humi=65.4% BAT=2459
```

#### 显示的数据包括：
- **加速度计数据**: Accel[X,Y,Z]g 和总加速度 Total
- **陀螺仪数据**: Gyro[X,Y,Z]°/s (角速度)
- **内置温度**: Temp (陀螺仪芯片温度)
- **步数**: Steps (计步器数据)
- **温湿度传感器**: THS_Temp (环境温度) 和 THS_Humi (湿度)
- **电池电压**: BAT (ADC原始值)

#### 错误处理显示：
- 温湿度传感器读取失败时显示 "THS_Temp=N/A THS_Humi=N/A"
- 所有数据都会实时显示在控制台，便于调试和监控

## 注意事项

1. 大数据模式会持续占用内存，建议在不需要时及时停止
2. TCP连接断开时数据仍会收集，重连后继续发送
3. 小数据模式是一次性操作，不会持续占用资源
4. 所有传感器数据包括陀螺仪、温湿度、电池电量都会一起发送
5. 按键功能已完全重构，不再支持OTA升级功能
6. **控制台现在显示详细的传感器数据**，便于实时监控和调试
