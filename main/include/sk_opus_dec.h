/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_opus_dec.h
 * @description: OPUS解码子模块接口
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#ifndef SK_OPUS_DEC_H
#define SK_OPUS_DEC_H

#include <stdint.h>
#include <freertos/FreeRTOS.h>
#include <freertos/queue.h>
#include "sk_audio_buffer.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef void* SkOpusDecHandler;

enum {
    AUDIO_IDX_0 = 0,
    AUDIO_IDX_1,
    AUDIO_IDX_2,
    AUDIO_IDX_3,
    AUDIO_IDX_4,
    AUDIO_IDX_5,
    AUDIO_IDX_6,
    AUDIO_IDX_7,
    AUDIO_IDX_8,
    AUDIO_IDX_9,
    AUDIO_IDX_POINT,
    AUDIO_IDX_CONFIGSTART,
    AUDIO_IDX_CONFIGEND,
    AUDIO_IDX_REBOOT,
    AUDIO_IDX_CONNECTED,
    AUDIO_IDX_ONHOOK,
    AUDIO_IDX_WAIT,
    AUDIO_IDX_LINKOK,
    AUDIO_IDX_START,
    AUDIO_IDX_NEED_CFG,
    AUDIO_IDX_CONNECTING,
    AUDIO_IDX_HERE,
    AUDIO_IDX_VOICE_TO,
    AUDIO_IDX_TEN,
    AUDIO_IDX_MAX,
    AUDIO_IDX_MIN,
    AUDIO_IDX_LOST_CONNECT,
    AUDIO_IDX_SIU,
    AUDIO_IDX_ENTER_PM,
    AUDIO_IDX_DONE,
    AUDIO_IDX_OK,
    LOCAL_PLAY_MAX_CNT,
};

enum {
    SK_OPUS_STATE_SPEECH = 0,
    SK_OPUS_STATE_DTX = 1,
};

SkOpusDecHandler SkOpusDecInit(int sampleRate, int channels, int durationMs, QueueHandle_t msgQueue);
void SkOpusDecDeinit(SkOpusDecHandler handler);
void SkOpusDecRegAudio(char* name, const uint8_t* data, int length, const uint16_t *pktOffset, int16_t pktCnt);
void SkOpusDecStop();
uint32_t SkOpusDecPlayLocal(uint8_t *audioList, int32_t len);
int32_t SkOpusDecPlayRemote(SkOpusDecHandler handler, uint16_t sessionId, uint8_t *data,
    int32_t len, SkAudioDownlinkTimeRecord *timeRecord);
void SkOpusDecRemoteDataEnd(SkOpusDecHandler handler, uint16_t sessionId);
int32_t SkOpusDecRemoteEnd(void);
size_t SkOpusDecFeedPlayAudio(uint16_t *buff, size_t len, SkAudioDownlinkTimeRecord *timeRecord);
SkOpusDecHandler SkOpusDecGetHandler();
void SkOpusDecStat();
void SkOpusDecMute(uint8_t muteFlag);
void SkOpusDecSetLoopback(uint8_t loopback);
int32_t SkOpusDecGetPlayState();
void SkOpusDecPutDataIntoQueue(SkOpusDecHandler handler, SkAudioBuf *inBuf);
void SkOpusDecDisplayQueueInfo();
void SkOpusDecEnableRemote(uint8_t enable);
int32_t SkOpusDecMuteRemote();
int32_t SkOpusDecUnmuteRemote();
void SkOpusDecStartLocalPlay(SkOpusDecHandler handler);
bool SkOpusDecLocalData(SkOpusDecHandler handler);
bool SkOpusDecRemote(SkOpusDecHandler handler);
bool SkOpusDecProc(SkOpusDecHandler handler);

#ifdef __cplusplus
}
#endif

#endif