/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_rmt_led.c
 * @description: RMT控制模式的LED灯驱动.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <string.h>
#include <math.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include "driver/rmt_tx.h"
#include "sk_board.h"

#define RMT_LED_STRIP_RESOLUTION_HZ 10000000 // 10MHz resolution, 1 tick = 0.1us (led strip needs a high resolution)
#define RMT_LED_STRIP_GPIO_NUM      21

#define EXAMPLE_LED_NUMBERS         2

#define EXAMPLE_FRAME_DURATION_MS   20
#define EXAMPLE_ANGLE_INC_FRAME     0.02
#define EXAMPLE_ANGLE_INC_LED       0.3

typedef struct {
    rmt_channel_handle_t rmtChan;
    rmt_encoder_handle_t encoder;
} SkRmtLedHandle_t;

SkRmtLedHandle_t g_ledHandle;

#ifdef CONFIG_RMT_LED_ENABLE
static const char *TAG = "SkLed";

static uint8_t led_strip_pixels[EXAMPLE_LED_NUMBERS * 3];

static const rmt_symbol_word_t ws2812_zero = {
    .level0 = 1,
    .duration0 = 0.3 * RMT_LED_STRIP_RESOLUTION_HZ / 1000000, // T0H=0.3us
    .level1 = 0,
    .duration1 = 0.9 * RMT_LED_STRIP_RESOLUTION_HZ / 1000000, // T0L=0.9us
};

static const rmt_symbol_word_t ws2812_one = {
    .level0 = 1,
    .duration0 = 0.9 * RMT_LED_STRIP_RESOLUTION_HZ / 1000000, // T1H=0.9us
    .level1 = 0,
    .duration1 = 0.3 * RMT_LED_STRIP_RESOLUTION_HZ / 1000000, // T1L=0.3us
};

//reset defaults to 50uS
static const rmt_symbol_word_t ws2812_reset = {
    .level0 = 0,
    .duration0 = RMT_LED_STRIP_RESOLUTION_HZ / 1000000 * 50 / 2,
    .level1 = 0,
    .duration1 = RMT_LED_STRIP_RESOLUTION_HZ / 1000000 * 50 / 2,
};

uint8_t g_ledEventMap[SK_LED_EVENT_MAX][6] = {
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00},   //SK_LED_EVENT_DOWN
    {0x01, 0x01, 0x00, 0x00, 0x00, 0x00},   //SK_LED_EVENT_INIT
    {0x00, 0x00, 0x01, 0x00, 0x00, 0x00},   //SK_LED_EVENT_CONNECTING
    {0x00, 0x00, 0x01, 0x01, 0x00, 0x00},   //SK_LED_EVENT_OTA
    {0x04, 0x04, 0x04, 0x00, 0x04, 0x00},   //SK_LED_EVENT_IDLE
    {0x04, 0x04, 0x04, 0x04, 0x00, 0x00},   //SK_LED_EVENT_ERROR
    {0x00, 0x01, 0x00, 0x00, 0x01, 0x00},   //SK_LED_EVENT_CHAT
    {0x00, 0x01, 0x00, 0x00, 0x00, 0x01},   //SK_LED_EVENT_STORY
    {0x00, 0x01, 0x00, 0x00, 0x01, 0x01},   //SK_LED_EVENT_MUSIC
    {0x01, 0x01, 0x01, 0x01, 0x00, 0x00},   //SK_LED_EVENT_CALL_INCOMING
    {0x01, 0x01, 0x01, 0x00, 0x00, 0x01},   //SK_LED_EVENT_CALL_OUTGOING
    {0x01, 0x01, 0x01, 0x00, 0x01, 0x00},   //SK_LED_EVENT_CALLING
    {0x04, 0x04, 0x04, 0x04, 0x04, 0x04},   //SK_LED_EVENT_CONFIG
    {0x04, 0x00, 0x00, 0x00, 0x04, 0x00},   //SK_LED_EVENT_REBOOT
};

static size_t encoder_callback(const void *data, size_t data_size,
                               size_t symbols_written, size_t symbols_free,
                               rmt_symbol_word_t *symbols, bool *done, void *arg)
{
    // We need a minimum of 8 symbol spaces to encode a byte. We only
    // need one to encode a reset, but it's simpler to simply demand that
    // there are 8 symbol spaces free to write anything.
    if (symbols_free < 8) {
        return 0;
    }

    // We can calculate where in the data we are from the symbol pos.
    // Alternatively, we could use some counter referenced by the arg
    // parameter to keep track of this.
    size_t data_pos = symbols_written / 8;
    uint8_t *data_bytes = (uint8_t*)data;
    if (data_pos < data_size) {
        // Encode a byte
        size_t symbol_pos = 0;
        for (int bitmask = 0x80; bitmask != 0; bitmask >>= 1) {
            if (data_bytes[data_pos]&bitmask) {
                symbols[symbol_pos++] = ws2812_one;
            } else {
                symbols[symbol_pos++] = ws2812_zero;
            }
        }
        // We're done; we should have written 8 symbols.
        return symbol_pos;
    } else {
        //All bytes already are encoded.
        //Encode the reset, and we're done.
        symbols[0] = ws2812_reset;
        *done = 1; //Indicate end of the transaction.
        return 1; //we only wrote one symbol
    }
}
#endif

void SkRledInit() {
#ifdef CONFIG_RMT_LED_ENABLE
    SkRmtLedHandle_t *handle = &g_ledHandle;
    rmt_tx_channel_config_t chanConfig = {
        .clk_src = RMT_CLK_SRC_DEFAULT, // select source clock
        .gpio_num = RMT_LED_STRIP_GPIO_NUM,
        .mem_block_symbols = 64, // increase the block size can make the LED less flickering
        .resolution_hz = RMT_LED_STRIP_RESOLUTION_HZ,
        .trans_queue_depth = 4, // set the number of transactions that can be pending in the background
    };

    ESP_LOGI(TAG, "Create RMT TX channel");
    handle->rmtChan = NULL;
    handle->encoder = NULL;
    ESP_ERROR_CHECK(rmt_new_tx_channel(&chanConfig, &handle->rmtChan));

    ESP_LOGI(TAG, "Create simple callback-based encoder");
    const rmt_simple_encoder_config_t encoderCfg = {
        .callback = encoder_callback
        //Note we don't set min_chunk_size here as the default of 64 is good enough.
    };
    ESP_ERROR_CHECK(rmt_new_simple_encoder(&encoderCfg, &handle->encoder));

    ESP_LOGI(TAG, "Enable RMT TX channel");
    ESP_ERROR_CHECK(rmt_enable(handle->rmtChan));
#endif
    return;
}

void SkRledDeinit() {
#ifdef CONFIG_RMT_LED_ENABLE
    SkRmtLedHandle_t *handle = &g_ledHandle;
    ESP_ERROR_CHECK(rmt_disable(handle->rmtChan));
    ESP_ERROR_CHECK(rmt_del_channel(handle->rmtChan));
    ESP_ERROR_CHECK(rmt_del_encoder(handle->encoder));
#endif
    return;
}

void SkRledSetEvent(uint32_t event) {
#ifdef CONFIG_RMT_LED_ENABLE
    if (event >= SK_LED_EVENT_MAX) {
        return;
    }

    // 将Event转换为RGB灯语
    memcpy(led_strip_pixels, g_ledEventMap[event], sizeof(g_ledEventMap[event]));
    
    // Flush RGB values to LEDs
    rmt_transmit_config_t tx_config = {
        .loop_count = 1, // no transfer loop
    };
    ESP_ERROR_CHECK(rmt_transmit(g_ledHandle.rmtChan, g_ledHandle.encoder, led_strip_pixels, sizeof(led_strip_pixels), &tx_config));
    //ESP_ERROR_CHECK(rmt_tx_wait_all_done(led_chan, portMAX_DELAY));
#endif
    return;
}
