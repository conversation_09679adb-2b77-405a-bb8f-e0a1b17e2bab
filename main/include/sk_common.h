/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_common.h
 * @description: 通用定义
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#ifndef SK_COMMON_H
#define SK_COMMON_H

#ifdef __cplusplus
extern "C" {
#endif

#define SSID_MAX_CNT    16
#define SSID_MAX_LEN    32
#define PASSWORD_MAX_LEN 64

#define BSSID_LEN 6

#define MAX_FRIEND_CNT  8
#define MAX_FRIEND_NAME 32
#define MAX_FRIEND_ID   16

// 音频数据类型定义
#define SK_ENC_DATA_FLAG_VAD        (0x0001)
#define SK_ENC_DATA_FLAG_CMD        (0x0001 << 1)

#define OPUS_PKT_FLAG 0x4F  // 0x4F = 'O'

#define ARRAY_SIZE(arr) (sizeof(arr) / sizeof((arr)[0]) + __must_be_array(arr))
#define SK_MIN(a, b) ((a)>=(b)?(b):(a))
#define __must_be_array(arr) BUILD_BUG_ON_ZERO(__same_type((arr), &(arr)[0]))
#define __same_type(a, b) __builtin_types_compatible_p(typeof(a), typeof(b))
#define BUILD_BUG_ON_ZERO(e) ((int)(sizeof(struct { int:-!!(e); })))

enum {
    SK_RET_SUCCESS = 0,
    SK_RET_FAIL = 1,
    SK_RET_INVALID_PARAM = 2,
    SK_RET_NOT_IMPLEMENTED = 3,
    SK_RET_NO_MEMORY = 4,
    SK_RET_INVALID_OPERATION = 5,
    SK_RET_TIMEOUT = 6,
    SK_RET_INTERRUPTED = 7,
    SK_RET_NOT_FOUND = 8,
    SK_RET_ALREADY_EXISTS = 9,
    SK_RET_NOT_READY = 10,
};

enum {
    SK_ROLE_MODE_CALLER = 0,
    SK_ROLE_MODE_CALLEE = 1,
    SK_ROLE_MODE_UNKNOWN = 2,
};

enum {
    SK_WIFI_MODE_STA = 0,
    SK_WIFI_MODE_AP = 1,
    SK_WIFI_MODE_NULL = 2
};

typedef int32_t sk_err_t;

typedef struct __attribute__((packed)) {
    uint16_t seq;
    uint8_t len;
    uint8_t dataFlag;
    uint32_t encCollectTick; // ENC侧采集时间
    uint32_t encDoneTick;   // ENC侧编码完成时间
    uint32_t relayRxTick;   // Relay接收时间
    uint32_t relayTxTick;   // Relay发送时间
    uint32_t decRxTick;     // DEC侧从TCP流中解析出时间
    uint32_t decDoneTick;   // DEC侧解码完成时间
    uint32_t playTick;      // 播放时间
} SkAudioDownlinkTimeRecord;

typedef struct __attribute__((packed)) {
    uint16_t seq;
    uint8_t len;
    uint8_t dataFlag;
    uint32_t encDoneTick;   // ENC侧编码完成时间
    uint32_t encTxTick;     // ENC侧编码TX时间
    uint32_t relayRxTick;   // Relay接收时间
    uint32_t relayTxTick;   // Relay发送时间
    uint32_t decRxTick;     // DEC侧从TCP流中解析出时间
    uint32_t decStartTick;  // DEC侧解码完成时间
    uint32_t playTick;      // 播放时间
} SkAudioUplinkTimeRecord;

typedef struct __attribute__((packed)) {
    uint32_t timestamp;     // timestamp
    uint16_t battery;
    int16_t temp;           // temperature
    int16_t accel[3];       // accelerometer
    int16_t gyro[3];        // gyroscope
    uint16_t ff;            // free fall duration
    uint16_t step;          // step count
    uint16_t stepState;     // pedometer state
    uint16_t humi;
    uint16_t resv[2];
} SkDeviceInfoRecord;

typedef void* SkStateHandler;

#ifdef __cplusplus
}
#endif

#endif