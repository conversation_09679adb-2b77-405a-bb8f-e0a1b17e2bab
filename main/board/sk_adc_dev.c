/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_adc_dev.c
 * @description: ADC设备驱动， 包含电源能量读取和模拟按键.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "soc/soc_caps.h"
#include "freertos/FreeRTOS.h"
#include "esp_err.h"
#include "esp_log.h"
#include "sk_board_def.h"
#include "sk_aec_board.h"
// ESP-IDF 5.x ADC头文件
#include "esp_adc/adc_oneshot.h"
#include "esp_adc/adc_cali.h"
#include "esp_adc/adc_cali_scheme.h"
#include "sk_board.h"
#include "sk_common.h"
#include "sk_os.h"

#define TAG "SkBsp"

#define BAT_ADC_LOW_THRESHOLD   1780
#define BAT_ADC_HIGH_THRESHOLD  2265
#define BAT_ADC_RANGE           (BAT_ADC_HIGH_THRESHOLD - BAT_ADC_LOW_THRESHOLD)

typedef struct {
    adc_oneshot_unit_handle_t adcHandle;
} SkAdcDev;

SkAdcDev g_adcDev;

int32_t SkBspAdcInit() {
#ifdef CONFIG_ADC_DEV_ENABLE
    SkAdcDev *adcDev = &g_adcDev;
    adc_oneshot_unit_init_cfg_t unitConfig = {
        .unit_id = BAT_ADC_UNIT,
        .ulp_mode = ADC_ULP_MODE_DISABLE,
    };
    adc_oneshot_chan_cfg_t chanConfig = {
        .atten = ADC_ATTEN_DB_12,
        .bitwidth = ADC_BITWIDTH_DEFAULT,
    };

    ESP_ERROR_CHECK(adc_oneshot_new_unit(&unitConfig, &adcDev->adcHandle));
    ESP_ERROR_CHECK(adc_oneshot_config_channel(adcDev->adcHandle, BAT_ADC_CHAN, &chanConfig));
#if (CONFIG_FUNC_KEY_MODE == CONFIG_SK_ADC_KEY)
    ESP_ERROR_CHECK(adc_oneshot_config_channel(adcDev->adcHandle, KEY_ADC_CHAN, &chanConfig));
#endif
#endif
    return ESP_OK;
}

void SkBspAdcDeinit() {
#ifdef CONFIG_ADC_DEV_ENABLE
    SkAdcDev *adcDev = &g_adcDev;

    ESP_ERROR_CHECK(adc_oneshot_del_unit(adcDev->adcHandle));
    adcDev->adcHandle = NULL;
#endif
    return;
}

int32_t SkBspGetAdcKey() {
#if (CONFIG_FUNC_KEY_MODE == CONFIG_SK_ADC_KEY)
    int adcRaw;
    int32_t key;
    SkAdcDev *adcDev = &g_adcDev;

    ESP_ERROR_CHECK(adc_oneshot_read(adcDev->adcHandle, KEY_ADC_CHAN, &adcRaw));
    //ESP_LOGI(TAG, "KEY Raw Data: %d", adcRaw);
    if (adcRaw > 2700) {
        key = 0;
    } else if (adcRaw > 2600) {
        key = 1;
    } else if (adcRaw > 2000) {
        key = 0;
    } else if (adcRaw > 1900) {
        key = 2;
    } else if (adcRaw > 1200) {
        key = 0;
    } else if (adcRaw > 900) {
        key = 3;
    } else if (adcRaw > 700) {
        key = 0;
    } else if (adcRaw > 600) {
        key = 4;
    } else {
        key = 0;
    }
#else
    int32_t key = 0;
#endif
    return key;
}

int32_t SkBspGetBattery() {
#ifdef CONFIG_ADC_DEV_ENABLE
    SkAdcDev *adcDev = &g_adcDev;
    int adcRaw, power;

    ESP_ERROR_CHECK(adc_oneshot_read(adcDev->adcHandle, BAT_ADC_CHAN, &adcRaw));
    if (adcRaw <= BAT_ADC_LOW_THRESHOLD) {
        power = 1;
    } else if (adcRaw >= BAT_ADC_HIGH_THRESHOLD) {
        power = 100;
    } else {
        power = ((adcRaw - BAT_ADC_LOW_THRESHOLD) * 100) / BAT_ADC_RANGE;
    }
    power = adcRaw;
    ESP_LOGI(TAG, "BAT Raw Data: %d Power: %d", adcRaw, power);
#else
    int32_t power = 100;
#endif    
    return power;
}
