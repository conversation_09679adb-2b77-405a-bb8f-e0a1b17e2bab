/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_board.h
 * @description: 单板接口定义头文件.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#ifndef SK_BOARD_H
#define SK_BOARD_H

#pragma once

#include <stdbool.h>
#include "sk_common.h"

#ifdef __cplusplus
extern "C" {
#endif

#define CONFIG_MIC_ENABLE       1
#define CONFIG_SPK_ENABLE       1

#define SK_KEY_PRESS_DOWN       0x0
#define SK_KEY_PRESS_UP         0x1

#define CONFIG_SK_PM_SLEEP_MODE         (CONFIG_SK_PM_SLEEP_MODE_LIGHT)
#define CONFIG_SK_PM_SLEEP_MODE_IDLE    (0)
#define CONFIG_SK_PM_SLEEP_MODE_LIGHT   (1)
#define CONFIG_SK_PM_SLEEP_MODE_LIGHT2  (2)
#define CONFIG_SK_PM_SLEEP_MODE_DEEP    (3)

#define MAX_PLAY_SAMPLE_PER_CALL 800*2
//#define CONFIG_NONCODEC_DEV 1

typedef struct {
    uint32_t timestamp;     // timestamp
    int16_t accel[3];       // accelerometer
    int16_t gyro[3];        // gyroscope
    uint16_t ff;            // free fall duration
    int16_t temp;           // temperature
    uint16_t step;          // step count
    uint16_t stepState;     // pedometer state
    uint16_t readFailFlags; // I2C read failure flags: bit0=accel, bit1=gyro, bit2=ff, bit3=pedometer
    uint16_t resv[1];
} SkSensorData_t;

typedef struct {
    int32_t temp;
    int32_t humi;
} SkTHSData_t;

sk_err_t SkBspBoardInit(uint32_t sampleRate, int bitsPerChan);

sk_err_t SkBspStartSpk(void);

sk_err_t SkBspStopSpk(void);

sk_err_t SkBspPlayAudio(const int16_t* data, size_t length, uint32_t msToWait);

sk_err_t SkBspPlayPreload(const int16_t* data, size_t length, size_t *w_bytes);

void SkBspEnableMic(bool flag);

sk_err_t SkBspReadAudio(int16_t *buffer, int bufferLen);


sk_err_t SkBspSetPlayVol(int volume);
int32_t SkBspGetPlayVol();
sk_err_t SkBspSetMicVol(int volume);
int32_t SkBspGetMicVol();

void SkBspShowStat();

void SkBspInitSensor();
int32_t SkGyroReadData(SkSensorData_t *data);
void SkGyroReset();

void SkBspEnterSleep(void);
void SkBspKeyInit(void);
void SkBoardSetPm(bool flag);
void SkBoardModemSetPm(bool flag);

enum {
    SK_LED_EVENT_DOWN = 0,
    SK_LED_EVENT_INIT = 1,
    SK_LED_EVENT_CONNECTING = 2,
    SK_LED_EVENT_OTA = 3,
    SK_LED_EVENT_IDLE = 4,
    SK_LED_EVENT_ERROR = 5,
    SK_LED_EVENT_CHAT = 6,
    SK_LED_EVENT_STORY = 7,
    SK_LED_EVENT_MUSIC = 8,
    SK_LED_EVENT_CALL_INCOMING = 9,
    SK_LED_EVENT_CALL_OUTGOING = 10,
    SK_LED_EVENT_CALLING = 11,
    SK_LED_EVENT_CONFIG = 12,
    SK_LED_EVENT_REBOOT = 13,
    SK_LED_EVENT_MAX = 14,
};

int32_t SkBspGetFuncKey();

void SkRledInit();
void SkRledDeinit();
void SkRledSetEvent(uint32_t event);

int32_t SkTHSReadData(SkTHSData_t *thsData);

int32_t SkBspGetHeadPhoneState();

int32_t SkBspAdcInit();
void SkBspAdcDeinit();
int32_t SkBspGetAdcKey();
int32_t SkBspGetBattery();


void SkBspKeyInit();
int32_t SkBspReadUserInput();
void SkBspHeadPhoneGpioInit();
void SkBspVibrationGpioInit();
int32_t SkBspGetHeadPhoneState();
int32_t SkBspGetVibrationState();
int32_t SkBspGetFuncKeyState();
int32_t SkBspGetChipID(uint8_t *chipID, size_t bufSize);

int32_t SkBspPmTimeWakeup(int32_t wakeupSec);

#ifdef __cplusplus
}
#endif

#endif