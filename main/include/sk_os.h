/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_os.h
 * @description: 操作系统适配模块接口.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#ifndef SK_OS_H
#define SK_OS_H

#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>
#include "freertos/FreeRTOS.h"

#ifdef __cplusplus
extern "C" {
#endif

#define CONFIG_PCM_DBG_CLOSE        0
#define CONFIG_PCM_DBG_POS_VC_AFE   1
#define CONFIG_PCM_DBG_POS_REC      2
#define CONFIG_PCM_DBG_POS_SR_AFE   3
#define CONFIG_PCM_DBG_POS_RB_IN    4
#define CONFIG_PCM_DBG_POS_RB_OUT   5
#define CONFIG_PCM_DBG_POS_ENC_QUEUE 6
#define CONFIG_PCM_DBG_POS_REC_CH0  7
#define CONFIG_PCM_DBG_POS_REC_CH1  8
#define CONFIG_PCM_DEBUG            CONFIG_PCM_DBG_POS_REC

#define CONFIG_MEMORY_USAGE_DEBUG
#ifdef CONFIG_MEMORY_USAGE_DEBUG
#define SK_OS_MODULE_MEM_STAT(name, flag) SkOsStatModuleMem(name, flag)
#else
#define SK_OS_MODULE_MEM_STAT(name, flag)
#endif

#define SK_OS_TASK_END() do { \
    vTaskDelete(NULL); \
} while (0)

typedef struct {
    uint64_t sec;
    uint32_t ms;
    uint32_t tickCnt;   // 累计tick数, 10ms一个tick
} SkBoardDateTime;

inline uint32_t SkOsGetTickCnt() { return xTaskGetTickCount(); }

int32_t SkOsGetCpuUsageArray(uint32_t *stackBase, uint16_t *cpuCnt, uint32_t *totalCnt, uint32_t maxCnt);
void SkOsShowMemUsage();
void SkOsStatModuleMem(char *name, bool endFlag);
void SkOsGetTaskNames();
void SkOsStartDebugLink();
void SkOsSendDebugData(uint8_t *data, uint32_t len);
void SkOsReboot();
void SkOsShowCurrTime();
void SkOsGetTimeInfo(uint64_t *sec, uint32_t *msec, uint32_t *tickCnt);
void SkOsShowSysInfo();
void SkOsShowCpuInfo();
void* SkOsAllocPsram(size_t size, uint32_t align);
void SkOsStopDebugLink();
void SkOsShowArray(uint8_t *data, uint32_t len);
void SkOsShowPointerArray(uint8_t *data, uint32_t len);

#ifdef __cplusplus
}
#endif

#endif /* SK_OS_H */