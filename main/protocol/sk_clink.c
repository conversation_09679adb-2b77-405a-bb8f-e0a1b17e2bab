/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_clink.c
 * @description: 控制链路.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <stdlib.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/i2s_std.h"
#include "driver/gpio.h"
#include "esp_check.h"
#include "sdkconfig.h"
#include "esp_log.h"

#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <errno.h>
#include <netdb.h>            // struct addrinfo
#include <arpa/inet.h>
#include "esp_netif.h"
#include "esp_timer.h" 
#include "sk_common.h"
#include "sk_frame.h"
#include "sk_clink.h"
#include "sk_rlink.h"
#include "sk_sm.h"
#include "sk_config.h"
#include "sk_board.h"

#define CLINK_MSG_QUEUE_SIZE    8
#define CLINK_RX_BUF_SIZE       256
#define CLINK_RX_MSG_BUF_SIZE   1024

#define TAG "SkClink"

enum {
    CLINK_SOCK_NOT_RDY = 0,
    CLINK_SOCK_RDY = 1,
};

enum {
    CLINK_STATE_IDLE = 0,
    CLINK_STATE_BUSY = 1,
};

typedef struct {
    uint8_t event;      // 事件
    uint8_t resv[3];
    int32_t param1;     // 参数
    int32_t param2;     // 参数
    int32_t param3;     // 参数
} ClinkMsg;

typedef struct {
    char serverIp[16];
    char chatId[17];
    uint8_t calleeId[6];
    int sock;
    int state;
    uint16_t port;
    uint16_t sockRdy;
    uint8_t reportLoopCnt;
    uint8_t runFlag;
    uint16_t sessionID;
    uint8_t *rxBuf;
    uint8_t *msgBuf;
    SkStateHandler handler;
    TaskHandle_t taskHandle;
    QueueHandle_t msgQueue;
    esp_timer_handle_t timer;
} ClinkCtrlInfo;

ClinkCtrlInfo g_clinkCtrl = {
    .serverIp = "***********",
    .port = 9527, 
    .sockRdy = CLINK_SOCK_NOT_RDY,
    .reportLoopCnt = 0,
    .runFlag = CLINK_RUN_FLAG_IDLE,
    .sock = -1,
    .msgQueue = NULL,
    .timer = NULL,
    .state = CLINK_STATE_IDLE,
};

int ClinkConnect(void) {
	int ret, sock, flags;
	uint32_t addr;
    struct sockaddr_in ack_sock_addr;

    memset(&ack_sock_addr, 0, sizeof(struct sockaddr));
    ack_sock_addr.sin_family = AF_INET;
    sock = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);

    strcpy(g_clinkCtrl.serverIp, SkConfigGetCtrlServer());
    ESP_LOGD(TAG, "Clink server addr: %s", g_clinkCtrl.serverIp);
	addr = inet_addr(g_clinkCtrl.serverIp);
	memcpy((char *)&ack_sock_addr.sin_addr, (char *)&addr, sizeof(addr));
    ack_sock_addr.sin_port = htons(g_clinkCtrl.port);

	ret = connect(sock, (struct sockaddr *)&ack_sock_addr, sizeof(struct sockaddr));
    if (ret != 0) {
        ESP_LOGD(TAG, "connect ack failed! socket num=%d ret %d", sock, ret);
        closesocket(sock);
        return SK_RET_FAIL;
    }
    flags = fcntl(sock, F_GETFL, 0);
    fcntl(sock, F_SETFL, flags | O_NONBLOCK);
    g_clinkCtrl.chatId[16] = '\0';
	g_clinkCtrl.sock = sock;
	g_clinkCtrl.sockRdy = CLINK_SOCK_RDY;

    return SK_RET_SUCCESS;
}

void ClinkDisconnect(void) {
    ClinkCtrlInfo *ctrl = &g_clinkCtrl;
	if (ctrl->sockRdy == CLINK_SOCK_RDY) {
	    closesocket(ctrl->sock);
		ctrl->sock = -1;
		ctrl->sockRdy = CLINK_SOCK_NOT_RDY;
	}

    return;
}

int SkClinkEventNotify(uint8_t event, int32_t param1, int32_t param2, int32_t param3) {
    ClinkMsg msg;
    if (g_clinkCtrl.msgQueue == NULL) {
        return SK_RET_FAIL;
    }
    msg.event = event;
    msg.param1 = param1;
    msg.param2 = param2;
    msg.param3 = param3;
    xQueueSend(g_clinkCtrl.msgQueue, &msg, portMAX_DELAY);
    return SK_RET_SUCCESS;
}

int ClinkSendRemoteMsg(CtrlMsgFrame *msg) {
    int ret;

    ret = send(g_clinkCtrl.sock, (char *)msg, sizeof(CtrlMsgFrame), 0);
    if (ret != sizeof(CtrlMsgFrame)) {
        ESP_LOGE(TAG, "send msg %d failed! ret=%d", msg->frameHead.msgType, ret);
        return SK_RET_FAIL;
    }
    // ESP_LOGI(TAG, "send msg %d success!", msg->frameHead.msgType);
    return SK_RET_SUCCESS;
}

int ClinkSendMsgRegisterTerm(void) {
    CtrlMsgFrame msg;
    EncodeRegisterReqMsg(&msg);
    return ClinkSendRemoteMsg(&msg);
}

int ClinkSendMsgCallReq(ClinkCtrlInfo *ctrl) {
    CtrlMsgFrame msg;
    EncodeCallRequestMsg(&msg, 1);
    msg.ctrlMsg.callerReserve = ctrl->sessionID;
    return ClinkSendRemoteMsg(&msg);
}

int ClinkSendMsgCallTerm(ClinkCtrlInfo *ctrl, uint8_t calleeTerminalID[6]) {
    CtrlMsgFrame msg;
    EncodeCallRequestMsg(&msg, 1);
    msg.ctrlMsg.callerReserve = ctrl->sessionID;
    memcpy(msg.ctrlMsg.calleeTerminalID, calleeTerminalID, 6);
    memcpy(msg.ctrlMsg.calleeRoleID, calleeTerminalID, 6);
    return ClinkSendRemoteMsg(&msg);
}

int ClinkSendTermState(void) {
    CtrlMsgFrame msg;
    if (g_clinkCtrl.state == CLINK_STATE_BUSY) {
        EncodeStateReportMsg(&msg, MSG_TERM_TO_CTRL_CALLER_CALLING_NOTICE, (uint8_t *)g_clinkCtrl.chatId);
    } else {
        EncodeStateReportMsg(&msg, MSG_TERM_TO_CTRL_CALL_IDLE_NOTICE, (uint8_t *)g_clinkCtrl.chatId);
    }
    return ClinkSendRemoteMsg(&msg);
}

void ClinkTimerCallback(void* arg) {
    ClinkCtrlInfo *ctrl = (ClinkCtrlInfo *)arg;
    // ESP_LOGI(TAG, "Timer triggered!");
    if (ctrl->sockRdy == CLINK_SOCK_NOT_RDY) {
        return;
    }
    ctrl->reportLoopCnt++;
    if (ctrl->reportLoopCnt >= 30) {
        // ESP_LOGI(TAG, "Clink report time reach!");
        SkClinkEventNotify(CLINK_EVENT_STATE_REPORT, 0, 0, 0);
        ctrl->reportLoopCnt = 0;
    }
    return;
}

void ClinkStartTimer(ClinkCtrlInfo *ctrl) {
    const esp_timer_create_args_t timerArgs = {
        .callback = &ClinkTimerCallback,
        .name = "ClinkTimer",
        .arg = ctrl,
    };

    if (ctrl->timer != NULL) {
        return;
    }
    ESP_ERROR_CHECK(esp_timer_create(&timerArgs, &ctrl->timer));
    // 设置定时器为周期性触发，每1秒触发一次
    ESP_ERROR_CHECK(esp_timer_start_periodic(ctrl->timer, 1000 * 1000)); // 时间单位为微秒
    return;
}

void ClinkStopTimer(ClinkCtrlInfo *ctrl) {
    if (ctrl->timer != NULL) {
        esp_timer_stop(ctrl->timer);
        esp_timer_delete(ctrl->timer);
        ctrl->timer = NULL;
    }
    return;
}

int ClinkInit(SkStateHandler handler) {
    g_clinkCtrl.handler = handler;
    g_clinkCtrl.reportLoopCnt = 0;
    g_clinkCtrl.runFlag = CLINK_RUN_FLAG_IDLE;
    g_clinkCtrl.rxBuf = (uint8_t *)malloc(CLINK_RX_BUF_SIZE);
    g_clinkCtrl.msgBuf = (uint8_t *)malloc(CLINK_RX_MSG_BUF_SIZE);
    g_clinkCtrl.msgQueue = xQueueCreate(CLINK_MSG_QUEUE_SIZE, sizeof(ClinkMsg));

    return SK_RET_SUCCESS;
}

void ClinkDeinit(void) {
    esp_err_t err = esp_timer_stop(g_clinkCtrl.timer);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to stop timer: %s", esp_err_to_name(err));
    } else {
        ESP_LOGI(TAG, "Timer stopped successfully");
    }

    // 删除定时器
    err = esp_timer_delete(g_clinkCtrl.timer);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to delete timer: %s", esp_err_to_name(err));
    } else {
        ESP_LOGI(TAG, "Timer deleted successfully");
    }

    vQueueDelete(g_clinkCtrl.msgQueue);
}

int ClinkCheckMsgValid(CtrlMsgFrame *msg) {
    if (ntohs(msg->frameHead.headFlag) != FRAME_HEAD_FLAG ||
        msg->frameHead.frameType != FRAME_CMSG_CTRL_TO_TERM ||
        ntohs(msg->frameHead.headLen) != sizeof(FrameHead) ||
        ntohs(msg->frameHead.payloadLen) != sizeof(SessionHead) + sizeof(CtrlMsg)) {
        return SK_RET_FAIL;
    }
    return SK_RET_SUCCESS;
}

int ClinkProcRemoteMsg(ClinkCtrlInfo *ctrl, uint8_t *data, int dataLen) {
    CtrlMsgFrame *msg = (CtrlMsgFrame *)data;

    if (ClinkCheckMsgValid(msg) != SK_RET_SUCCESS) {
        ESP_LOGE(TAG, "Remote msg invalid!");
        return SK_RET_FAIL;
    }

    switch (msg->ctrlMsg.msgType) {
        case MSG_CTRL_TO_TERM_REGISTER_OK:
            ESP_LOGI(TAG, "CtrlMsg: Register result OK!");
            SkSmSendEvent(ctrl->handler, SM_EVENT_LINK, SM_EVENT_CLINK_CONNECT, 0, 0);
            break;
        case MSG_CTRL_TO_TERM_REGISTER_REFUSE:
            ESP_LOGI(TAG, "CtrlMsg: Register result refuse!");
            break;
        case MSG_CTRL_TO_TERM_IDLE_OK:
        case MSG_CTRL_TO_TERM_ALIVE_OK:
            //ESP_LOGI(TAG, "CtrlMsg: State report ack");
            break;
        case MSG_CTRL_TO_TERM_CALLER_REQUEST_OK:
            ESP_LOGI(TAG, "CtrlMsg: Caller request result ok");
            break;
        case MSG_CTRL_TO_TERM_CALLER_REQUEST_REFUSED:
            ctrl->chatId[16] = 0;
            memcpy(ctrl->chatId, msg->ctrlMsg.chatID, 16);
            ESP_LOGI(TAG, "CtrlMsg: Caller request result refuse %s", ctrl->chatId);
            SkSmSendEvent(ctrl->handler, SM_EVENT_LINK, SM_EVENT_CLINK_RELAY_FAIL, 0, 0);
            break;
        case MSG_CTRL_TO_TERM_CONNECT_RELAY_AS_CALLER_REQUEST:
            if (ctrl->sessionID != msg->ctrlMsg.callerReserve) {
                ESP_LOGE(TAG, "Caller sessionID error %d to %d!", ctrl->sessionID, msg->ctrlMsg.callerReserve);
                return SK_RET_SUCCESS;
            }
            ctrl->chatId[16] = 0;
            memcpy(ctrl->chatId, msg->ctrlMsg.chatID, 16);
            ESP_LOGI(TAG, "CtrlMsg: relay info as caller %u %s", msg->ctrlMsg.callerReserve, ctrl->chatId);
            RlinkSetCallReqInfo(SK_ROLE_MODE_CALLER, msg->ctrlMsg.relayIP, ntohs(msg->ctrlMsg.relayPort), &msg->ctrlMsg);
            SkSmSendEvent(ctrl->handler, SM_EVENT_LINK, SM_EVENT_CLINK_RELAY_OK, ctrl->sessionID, 0);
            break;
        case MSG_CTRL_TO_TERM_CONNECT_RELAY_AS_CALLEE_REQUEST:
            ESP_LOGI(TAG, "CtrlMsg: relay info as callee");
            memcpy(ctrl->chatId, msg->ctrlMsg.chatID, SK_MIN(sizeof(ctrl->chatId), sizeof(msg->ctrlMsg.chatID)));
            ctrl->chatId[sizeof(ctrl->chatId) - 1] = '\0';
            RlinkSetCallReqInfo(SK_ROLE_MODE_CALLEE, msg->ctrlMsg.relayIP, ntohs(msg->ctrlMsg.relayPort), &msg->ctrlMsg);
            SkSmSendEvent(ctrl->handler, SM_EVENT_LINK, SM_EVENT_CLINK_CALL_REQUEST, ctrl->sessionID, 0);
            break;
        case MSG_CTRL_TO_TERM_CALLING_OK:
            ESP_LOGI(TAG, "CtrlMsg: calling ok");
            break;
        default:
            ESP_LOGE(TAG, "Unknown msg type:%d", msg->ctrlMsg.msgType);
    }

    return SK_RET_SUCCESS;
}

int ClinkProcSockData(ClinkCtrlInfo *ctrl, uint8_t *buffer, int orgLen, int bufferLen, uint8_t *newData, int newDataLen) {
    int pos, dataLen;

    // 判断原数据长度和新数据长度之和是否大于Buffer长度，如果大于则返回-1
    if (orgLen + newDataLen > bufferLen) {
        ESP_LOGI(TAG, "buffer overflow!");
        return -1;
    }
    /* 新数据拷贝进入Buffer */
    memcpy(buffer + orgLen, newData, newDataLen);
    dataLen = orgLen + newDataLen;
    pos = 0;

    while (dataLen >= sizeof(CtrlMsgFrame)) {
        if (ClinkProcRemoteMsg(ctrl, &buffer[pos], dataLen) != SK_RET_SUCCESS) {
            ESP_LOGI(TAG, "ClinkProcRemoteMsg failed!");
            return -1;
        }

        pos += sizeof(CtrlMsgFrame);
        dataLen -= sizeof(CtrlMsgFrame);
    }
    if (dataLen > 0) {
        if (dataLen < pos) {
            memcpy(buffer, &buffer[pos], dataLen);
        } else {
            ESP_LOGI(TAG, "ClinkProcSockData: dataLen error: pos=%d, dataLen=%d!", pos, dataLen);
            return -1;
        }
    }

    return dataLen;
}

void SkClinkSetCalleeId(uint8_t calleeId[6]) {
    memcpy(g_clinkCtrl.calleeId, calleeId, sizeof(g_clinkCtrl.calleeId));
    return;
}

int ClinkProcLocalMsg(ClinkCtrlInfo *ctrl, QueueHandle_t msgQueue) {
    int ret = SK_RET_SUCCESS;
    ClinkMsg msg;

    if (xQueueReceive(msgQueue, &msg, 0)) {
        switch (msg.event) {
            case CLINK_EVENT_CALL_AGENT:
                ctrl->sessionID = (uint16_t)msg.param1;
                ret = ClinkSendMsgCallReq(ctrl);
                g_clinkCtrl.state = CLINK_STATE_BUSY;
                ESP_LOGI(TAG, "Clink send call req ret=%d local senssion %d!",
                    ret, ctrl->sessionID);
                break;
            case CLINK_EVENT_CALL_TERM:
                ctrl->sessionID = (uint16_t)msg.param1;
                ret = ClinkSendMsgCallTerm(ctrl, ctrl->calleeId);
                g_clinkCtrl.state = CLINK_STATE_BUSY;
                ESP_LOGI(TAG, "Clink send call req ret=%d local senssion %d!",
                    ret, ctrl->sessionID);
                break;
            case CLINK_EVENT_CALL_TERM_OK:
                ctrl->sessionID = (uint16_t)msg.param1;
                g_clinkCtrl.state = CLINK_STATE_BUSY;
                ret = ClinkSendTermState();
                ESP_LOGI(TAG, "Clink send state by accept call!");
                break;
            case CLINK_EVENT_STATE_REPORT:
                ret = ClinkSendTermState();
                //ESP_LOGI(TAG, "Clink send state report ret=%d!", ret);
                break;
            case CLINK_EVENT_FINISH_CALL:
                g_clinkCtrl.state = CLINK_STATE_IDLE;
                ret = ClinkSendTermState();
                break;
            case CLINK_EVENT_ENTER_PM_MODE:
                // 通知CtrlServer进入PM模式
                ESP_LOGI(TAG, "Clink need enter pm mode");
                ret = SK_RET_FAIL;
                break;
            default:
                ESP_LOGI(TAG, "Unknown event:%d", msg.event);
        }
    }
    return ret;
}

void ClinkLoop(void) {
    fd_set readfds;
    int bytes, activity, lastBytes, sock;
    struct timeval timeout = {0, 10000}; // 10ms超时
    ClinkCtrlInfo *ctrl = &g_clinkCtrl;
    uint8_t *buffer = ctrl->msgBuf;
    uint8_t *sockBuf = ctrl->rxBuf;

    sock = ctrl->sock;
    lastBytes = 0;
    while (ctrl->runFlag != CLINK_RUN_FLAG_STOP) {
        FD_ZERO(&readfds);
        FD_SET(sock, &readfds);

        activity = select(sock + 1, &readfds, NULL, NULL, &timeout);
        if ((activity > 0) && (FD_ISSET(sock, &readfds))) {
            // Socket有数据可读
            bytes = recv(sock, sockBuf, CLINK_RX_BUF_SIZE, 0);
            if (bytes <= 0) {
                // 连接已断开或异常
                ESP_LOGE(TAG, "Connection closed by remote host or abnormal");
                break;
            }
            lastBytes = ClinkProcSockData(ctrl, buffer, lastBytes, CLINK_RX_MSG_BUF_SIZE, sockBuf, bytes);
            if (lastBytes < 0) {
                ESP_LOGI(TAG, "ClinkProcSockData failed!");
                break;
            }
        }

        // 检查队列中是否有消息
        if (ClinkProcLocalMsg(ctrl, ctrl->msgQueue) != SK_RET_SUCCESS) {
            break;
        }
    }
}

void SkClinkSetFunFlag(uint8_t flag) {
    g_clinkCtrl.runFlag = flag;
}

void ClinkTask(void *arg) {
    ClinkCtrlInfo *ctrl = &g_clinkCtrl;

    while (ctrl->runFlag != CLINK_RUN_FLAG_STOP) {
        if (ctrl->runFlag == CLINK_RUN_FLAG_IDLE) {
            vTaskDelay(1000); // 等待连接成功标志位被设置
            continue;
        }
        
        SkSmSendEvent(ctrl->handler, SM_EVENT_LINK, SM_EVENT_CLINK_CONNECTING, 0, 0);
        if (ClinkConnect() == SK_RET_SUCCESS) {
            ClinkSendMsgRegisterTerm();
            ClinkLoop();
            ClinkDisconnect();
        }
        SkSmSendEvent(ctrl->handler, SM_EVENT_LINK, SM_EVENT_CLINK_DISCONNECT, 0, 0);
    }
    
    vTaskDelete(NULL);
}

void SkClinkStartTask() {
    xTaskCreate(ClinkTask, "ClinkTask", 4096, NULL, 5, &g_clinkCtrl.taskHandle);
    ESP_LOGI(TAG, "stack base %p", pxTaskGetStackStart(g_clinkCtrl.taskHandle));
    return;
}

int SkClinkInit(SkStateHandler handler) {
    if (ClinkInit(handler) != SK_RET_SUCCESS) {
        return SK_RET_FAIL;
    }
    ClinkStartTimer(&g_clinkCtrl);
    SkClinkStartTask();
    return SK_RET_SUCCESS;
}

void SkClinkSetPm(bool flag) {
    ClinkCtrlInfo *ctrl = &g_clinkCtrl;

    if (flag) {
#if CONFIG_SK_PM_SLEEP_MODE == CONFIG_SK_PM_SLEEP_MODE_LIGHT2
        ctrl->runFlag = CLINK_RUN_FLAG_STOP;
#else
        ctrl->runFlag = CLINK_RUN_FLAG_IDLE;
#endif
        ClinkStopTimer(ctrl);
        SkClinkEventNotify(CLINK_EVENT_ENTER_PM_MODE, 0, 0, 0);
    } else {
        ClinkStartTimer(ctrl);
#if CONFIG_SK_PM_SLEEP_MODE == CONFIG_SK_PM_SLEEP_MODE_LIGHT2
        ctrl->runFlag = CLINK_RUN_FLAG_IDLE;
        SkClinkStartTask();
#else
        g_clinkCtrl.runFlag = CLINK_RUN_FLAG_START;
#endif
    }

    return;
}
