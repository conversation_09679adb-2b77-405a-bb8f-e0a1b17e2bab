# copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
cmake_minimum_required(VERSION 3.5)

idf_component_register(
	SRC_DIRS
		./board
		./os
		./network
		./audio
		./protocol
		./app
		./config
		./opus_coder
		./dfx
	INCLUDE_DIRS
		"include"
	REQUIRES
		esp_http_server
		app_update
		esp_partition
		esp_wifi
		nvs_flash
		esp_adc
		driver
		fatfs
		spiffs
		esp_timer
		esp_event
		esp_netif
		esp_system
		esp_common
		freertos
		log
		vfs
		)
if(COVERAGE_ENABLED)
	target_compile_options(${COMPONENT_LIB} PRIVATE --coverage)
	target_link_options(${COMPONENT_LIB} PRIVATE --coverage)
endif()