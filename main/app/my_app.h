/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: my_app.h
 * @description: 自定义应用程序头文件 - 通过按键控制陀螺仪数据读取
 * @author: User
 * @date: 2025-07-12
 */

#ifndef MY_APP_H
#define MY_APP_H

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化我的应用程序
 * 
 * 按键控制陀螺仪读取：
 * - 按下按键开始读取陀螺仪数据
 * - 每分钟软件复位一次陀螺仪
 * - 再次按下按键停止读取
 */
void MyAppInit(void);

/**
 * @brief 反初始化我的应用程序
 */
void MyAppDeinit(void);

/**
 * @brief 获取当前读取状态
 */
void MyAppGetStatus(void);

#ifdef __cplusplus
}
#endif

#endif // MY_APP_H
