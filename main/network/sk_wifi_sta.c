/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_wifi_sta.c
 * @description: Wi-Fi的STA模式.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <string.h>
#include <freertos/FreeRTOS.h>
#include <freertos/event_groups.h>
#include <esp_log.h>
#include <esp_wifi.h>
#include <nvs.h>
#include "nvs_flash.h"
#include <esp_netif.h>
#include <esp_system.h>
#include <esp_timer.h>
#include "sk_wifi_sta.h"
#include "sk_common.h"
#include "sk_config.h"
#include "esp_sntp.h"

#define TAG "SkSta"
#define WIFI_EVENT_CONNECTED BIT0
#define MAX_RECONNECT_COUNT 5

#define WIFI_APP_EVENT_START BIT0
#define WIFI_APP_EVENT_NO_OPERATING BIT1

typedef struct  {
    char ssid[SSID_MAX_LEN + 1];
    char password[PASSWORD_MAX_LEN + 1];
    int channel;
    int authmode;
    int rssi;
    uint8_t bssid[BSSID_LEN];
} WifiApRecord;

typedef struct {
    EventGroupHandle_t eventGroup;      // WiFi驱动使用的事件组
    EventGroupHandle_t wifiEventGroup;  // 应用程序使用的事件组
    esp_timer_handle_t timerHandle;
    esp_event_handler_instance_t instance_any_id_;
    esp_event_handler_instance_t instance_got_ip_;
    char ssid[32];
    char bssid[6];
    char password[64];
    char ipAddress[16];
    int channel;
    int reconnectCount;
    int scanCnt;
    int ssidIndex;
    SkWifiOnConnectCB onConnectCB;
    SkWifiOnConnectedCB onConnectedCB;
    SkWifiOnScanBeginScanCB onScanBeginCB;
    WifiApRecord apRecord[SSID_MAX_CNT];
    esp_netif_t *netif;
} SkWifiStation;

typedef void* SkStaHandler;
SkWifiStation g_wifiStation;

void SkInitializeSntp() {
    ESP_LOGI(TAG, "Initializing SNTP");
    esp_sntp_setoperatingmode(SNTP_OPMODE_POLL); // 设置 SNTP 操作模式为轮询
    esp_sntp_setservername(0, "pool.ntp.org");   // 设置 NTP 服务器地址
    esp_sntp_setservername(1, "cn.pool.ntp.org");
    esp_sntp_setservername(2, "ntp1.aliyun.com");
    esp_sntp_init();                             // 初始化 SNTP
    setenv("TZ", "CST-8", 1);
    tzset();
}

void SkStaStop(SkStaHandler handler) {
    SkWifiStation *ctrl = (SkWifiStation *)handler;

    esp_sntp_stop();
    
    xEventGroupClearBits(ctrl->wifiEventGroup, WIFI_APP_EVENT_START);
    xEventGroupWaitBits(ctrl->wifiEventGroup, WIFI_APP_EVENT_NO_OPERATING, pdFALSE, pdFALSE, 10000 / portTICK_PERIOD_MS);

    if (ctrl->timerHandle != NULL) {
        esp_timer_stop(ctrl->timerHandle);
        esp_timer_delete(ctrl->timerHandle);
        ctrl->timerHandle = NULL;
    }

    // Reset the WiFi stack
    ESP_ERROR_CHECK(esp_wifi_stop());
    ESP_ERROR_CHECK(esp_wifi_deinit());
    
    // 取消注册事件处理程序
    if (ctrl->instance_any_id_ != NULL) {
        ESP_ERROR_CHECK(esp_event_handler_instance_unregister(WIFI_EVENT, ESP_EVENT_ANY_ID, ctrl->instance_any_id_));
        ctrl->instance_any_id_ = NULL;
    }
    if (ctrl->instance_got_ip_ != NULL) {
        ESP_ERROR_CHECK(esp_event_handler_instance_unregister(IP_EVENT, IP_EVENT_STA_GOT_IP, ctrl->instance_got_ip_));
        ctrl->instance_got_ip_ = NULL;
    }
    if (ctrl->netif != NULL) {
        esp_netif_destroy_default_wifi(ctrl->netif);
        ctrl->netif = NULL;
    }
}

void SkStaRegOnScanBegin(SkStaHandler handler, SkWifiOnScanBeginScanCB cb) {
    SkWifiStation *ctrl = (SkWifiStation *)handler;
    ctrl->onScanBeginCB = cb;
}

void SkStaRegOnConnect(SkStaHandler handler, SkWifiOnConnectCB cb) {
    SkWifiStation *ctrl = (SkWifiStation *)handler;
    ctrl->onConnectCB = cb;
}

void SkStaRegOnConnected(SkStaHandler handler, SkWifiOnConnectedCB cb) {
    SkWifiStation *ctrl = (SkWifiStation *)handler;
    ctrl->onConnectedCB = cb;
}

void SkStaScanTimerCallback(void *arg) {
    esp_wifi_scan_start(NULL, false);
    return;
}

// 快速排序的比较函数
int CompareRssi(const void *a, const void *b) {
    wifi_ap_record_t *ap_a = (wifi_ap_record_t *)a;
    wifi_ap_record_t *ap_b = (wifi_ap_record_t *)b;
    return ap_b->rssi - ap_a->rssi;  // 从大到小排序
}


void SkStaStartConnect(SkStaHandler handler) {
    SkWifiStation *ctrl = (SkWifiStation *)handler;

    if ((xEventGroupGetBits(ctrl->wifiEventGroup) & WIFI_APP_EVENT_START) == 0) {
        return;
    }

    if (ctrl->ssidIndex >= ctrl->scanCnt) {
        return;
    }
    
    strcpy(ctrl->ssid, ctrl->apRecord[ctrl->ssidIndex].ssid);
    strcpy(ctrl->password, ctrl->apRecord[ctrl->ssidIndex].password);
    memcpy(ctrl->bssid, ctrl->apRecord[ctrl->ssidIndex].bssid, 6);
    ctrl->channel = ctrl->apRecord[ctrl->ssidIndex].channel;
    ctrl->ssidIndex++;
    
    if (ctrl->onConnectCB != NULL) {
        ctrl->onConnectCB(ctrl->ssid);
    }

    wifi_config_t wifi_config;
    memset(&wifi_config, 0, sizeof(wifi_config));
    strcpy((char *)wifi_config.sta.ssid, ctrl->ssid);
    strcpy((char *)wifi_config.sta.password, ctrl->password);
    wifi_config.sta.channel = ctrl->channel;
    memcpy(wifi_config.sta.bssid, ctrl->bssid, 6);
    wifi_config.sta.bssid_set = true;
    wifi_config.sta.listen_interval = 10;
    ESP_ERROR_CHECK(esp_wifi_set_config(WIFI_IF_STA, &wifi_config));

    ctrl->reconnectCount = 0;
    ESP_LOGI(TAG, "Connect to AP: %s, BSSID: %02x:%02x:%02x:%02x:%02x:%02x, Channel: %d",
        (char *)wifi_config.sta.ssid, 
        wifi_config.sta.bssid[0], wifi_config.sta.bssid[1], wifi_config.sta.bssid[2],
        wifi_config.sta.bssid[3], wifi_config.sta.bssid[4], wifi_config.sta.bssid[5],
        wifi_config.sta.channel);
    ESP_ERROR_CHECK(esp_wifi_connect());
}

void SkStaHandleScanResult(SkStaHandler handler) {
    SkWifiStation *ctrl = (SkWifiStation *)handler;
    uint16_t ap_num = 0;
    wifi_ap_record_t *apRecords = NULL;
    wifi_ap_record_t *apRecord = NULL;
    SkSsidItem *ssidCfg = NULL;

    if ((xEventGroupGetBits(ctrl->wifiEventGroup) & WIFI_APP_EVENT_START) == 0) {
        return;
    }

    xEventGroupClearBits(ctrl->wifiEventGroup, WIFI_APP_EVENT_NO_OPERATING);
    esp_wifi_scan_get_ap_num(&ap_num);
    apRecords = (wifi_ap_record_t *)malloc(ap_num * sizeof(wifi_ap_record_t));
    if (apRecords == NULL) {
        ESP_LOGE(TAG, "Failed to allocate memory for AP records");
        xEventGroupSetBits(ctrl->wifiEventGroup, WIFI_APP_EVENT_NO_OPERATING);
        return;
    }

    esp_wifi_scan_get_ap_records(&ap_num, apRecords);
    qsort(apRecords, ap_num, sizeof(wifi_ap_record_t), CompareRssi);

    ctrl->scanCnt = 0;
    ctrl->ssidIndex = 0;
    for (int i = 0; i < ap_num; i++) {
        apRecord = &apRecords[i];
        ssidCfg = SkConfigGetSsid((char *)apRecord->ssid);
        if (ssidCfg == NULL) {
            continue;
        }
        strcpy(ctrl->apRecord[ctrl->scanCnt].ssid, (char *)apRecord->ssid);
        strcpy(ctrl->apRecord[ctrl->scanCnt].password, ssidCfg->password);
        ctrl->apRecord[ctrl->scanCnt].channel = apRecord->primary;
        ctrl->apRecord[ctrl->scanCnt].authmode = apRecord->authmode;
        memcpy(ctrl->apRecord[ctrl->scanCnt].bssid, apRecord->bssid, sizeof(apRecord->bssid));
        ctrl->apRecord[ctrl->scanCnt].rssi = apRecord->rssi;
        ESP_LOGI(TAG, "Found AP: %s, BSSID: %02x:%02x:%02x:%02x:%02x:%02x, RSSI: %d, Channel: %d, Authmode: %d",
            (char *)apRecord->ssid, 
            apRecord->bssid[0], apRecord->bssid[1], apRecord->bssid[2],
            apRecord->bssid[3], apRecord->bssid[4], apRecord->bssid[5],
            apRecord->rssi, apRecord->primary, apRecord->authmode);
        ctrl->scanCnt++;
    }
    free(apRecords);
    if (ctrl->scanCnt == 0) {
        ESP_LOGI(TAG, "Wait for next scan");
        esp_timer_start_once(ctrl->timerHandle, 10 * 1000);
    } else {
        SkStaStartConnect(handler);
    }
    
    xEventGroupSetBits(ctrl->wifiEventGroup, WIFI_APP_EVENT_NO_OPERATING);
    return;
}

// Static event handler functions
void SkStaWifiEventHandler(void* arg, esp_event_base_t event_base, int32_t event_id, void* event_data) {
    SkWifiStation *ctrl = (SkWifiStation *)&g_wifiStation;

    if ((xEventGroupGetBits(ctrl->wifiEventGroup) & WIFI_APP_EVENT_START) == 0) {
        return;
    }

    xEventGroupClearBits(ctrl->wifiEventGroup, WIFI_APP_EVENT_NO_OPERATING);
    if (event_id == WIFI_EVENT_STA_START) {
        esp_wifi_scan_start(NULL, false);
        if (ctrl->onScanBeginCB != NULL) {
            ctrl->onScanBeginCB();
        }
    } else if (event_id == WIFI_EVENT_SCAN_DONE) {
        SkStaHandleScanResult(ctrl);
    } else if (event_id == WIFI_EVENT_STA_DISCONNECTED) {
        xEventGroupClearBits(ctrl->eventGroup, WIFI_EVENT_CONNECTED);
        if (ctrl->reconnectCount < MAX_RECONNECT_COUNT) {
            ESP_ERROR_CHECK(esp_wifi_connect());
            ctrl->reconnectCount++;
            ESP_LOGI(TAG, "Reconnecting %s (attempt %d / %d)", ctrl->ssid, ctrl->reconnectCount, MAX_RECONNECT_COUNT);
            xEventGroupSetBits(ctrl->wifiEventGroup, WIFI_APP_EVENT_NO_OPERATING);
            return;
        }
        
        if (ctrl->ssidIndex < ctrl->scanCnt) {
            SkStaStartConnect(ctrl);
            xEventGroupSetBits(ctrl->wifiEventGroup, WIFI_APP_EVENT_NO_OPERATING);
            return;
        }
        
        ESP_LOGI(TAG, "No more AP to connect, wait for next scan");
        esp_timer_start_once(ctrl->timerHandle, 10 * 1000);
    } else if (event_id == WIFI_EVENT_STA_CONNECTED) {
    } else {
        ESP_LOGI(TAG, "event_base = %d event_id = %d", event_base, event_id);
    }
    xEventGroupSetBits(ctrl->wifiEventGroup, WIFI_APP_EVENT_NO_OPERATING);
}

void SkStaIpEventHandler(void* arg, esp_event_base_t event_base, int32_t event_id, void* event_data) {
    SkWifiStation *ctrl = (SkWifiStation *)&g_wifiStation;
    ip_event_got_ip_t* event = (ip_event_got_ip_t*)event_data;
    char ipAddress[16];

    if ((xEventGroupGetBits(ctrl->wifiEventGroup) & WIFI_APP_EVENT_START) == 0) {
        return;
    }

    xEventGroupClearBits(ctrl->wifiEventGroup, WIFI_APP_EVENT_NO_OPERATING);
    esp_ip4addr_ntoa(&event->ip_info.ip, ipAddress, sizeof(ipAddress));
    strcpy(ctrl->ipAddress, ipAddress);
    ESP_LOGI(TAG, "Got IP: %s", ctrl->ipAddress);
    SkInitializeSntp();
    xEventGroupSetBits(ctrl->eventGroup, WIFI_EVENT_CONNECTED);
    if (ctrl->onConnectedCB != NULL) {
        ctrl->onConnectedCB(ctrl->ssid);
    }
    ctrl->reconnectCount = 0;
    xEventGroupSetBits(ctrl->wifiEventGroup, WIFI_APP_EVENT_NO_OPERATING);
}

void SkStaStart(SkStaHandler handler) {
    SkWifiStation *ctrl = (SkWifiStation *)handler;
    esp_err_t ret;

    xEventGroupSetBits(ctrl->wifiEventGroup, WIFI_APP_EVENT_START);
    ctrl->scanCnt = 0;

    // Initialize the TCP/IP stack
    ESP_ERROR_CHECK(esp_netif_init());
    esp_log_level_set("wifi", ESP_LOG_ERROR);
    ret = esp_event_handler_instance_register(WIFI_EVENT, ESP_EVENT_ANY_ID, 
        &SkStaWifiEventHandler, ctrl, &ctrl->instance_any_id_);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to register WiFi event handler");
        return;
    }
    ret = esp_event_handler_instance_register(IP_EVENT, IP_EVENT_STA_GOT_IP,
        &SkStaIpEventHandler, ctrl, &ctrl->instance_got_ip_);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to register IP event handler");
        return;
    }

    // Create the default event loop
    ctrl->netif = esp_netif_create_default_wifi_sta();

    // Initialize the WiFi stack in station mode
    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    cfg.nvs_enable = false;
    ESP_ERROR_CHECK(esp_wifi_init(&cfg));
    ESP_ERROR_CHECK(esp_wifi_set_mode(WIFI_MODE_STA));
#if 0
    wifi_country_t country = {
        .cc = "CN",
        .schan = 1,
        .nchan = 13,
        .policy = WIFI_COUNTRY_POLICY_AUTO,
    };
    ESP_ERROR_CHECK(esp_wifi_set_country(&country));
    esp_wifi_set_max_tx_power(80); 
#endif
    ESP_ERROR_CHECK(esp_wifi_start());

    // Setup the timer to scan WiFi
    esp_timer_create_args_t timeArgs = {
        .callback = SkStaScanTimerCallback,
        .arg = ctrl,
        .dispatch_method = ESP_TIMER_TASK,
        .name = "WiFiScanTimer",
        .skip_unhandled_events = true
    };
    ESP_ERROR_CHECK(esp_timer_create(&timeArgs, &ctrl->timerHandle));
}

bool SkStaWaitForConnected(SkStaHandler handler, int timeoutMs) {
    SkWifiStation *ctrl = (SkWifiStation *)handler;

    EventBits_t bits = xEventGroupWaitBits(ctrl->eventGroup, WIFI_EVENT_CONNECTED, 
        pdFALSE, pdFALSE, timeoutMs / portTICK_PERIOD_MS);
    return (bits & WIFI_EVENT_CONNECTED) != 0;
}

int8_t SkStaGetRssi() {
    // Get station info
    wifi_ap_record_t ap_info;
    ESP_ERROR_CHECK(esp_wifi_sta_get_ap_info(&ap_info));
    return ap_info.rssi;
}

uint8_t SkStaGetChannel() {
    // Get station info
    wifi_ap_record_t ap_info;
    ESP_ERROR_CHECK(esp_wifi_sta_get_ap_info(&ap_info));
    return ap_info.primary;
}

bool SkStaIsConnected() {
    return xEventGroupGetBits(g_wifiStation.eventGroup) & WIFI_EVENT_CONNECTED;
}

void SkStaSetPowerSaveMode(bool enabled) {
    ESP_ERROR_CHECK(esp_wifi_set_ps(enabled ? WIFI_PS_MIN_MODEM : WIFI_PS_NONE));
}

SkStaHandler SkStaInit() {
    memset(&g_wifiStation, 0, sizeof(SkWifiStation));
    g_wifiStation.eventGroup = xEventGroupCreate();
    g_wifiStation.wifiEventGroup = xEventGroupCreate();
    xEventGroupSetBits(g_wifiStation.wifiEventGroup, WIFI_APP_EVENT_NO_OPERATING);

    return &g_wifiStation;
}

void SkStaDeinit(SkStaHandler handler) {
    SkWifiStation *ctrl = (SkWifiStation *)handler;

    if (ctrl == NULL) {
        return;
    }
    vEventGroupDelete(ctrl->eventGroup);
    return;
}
