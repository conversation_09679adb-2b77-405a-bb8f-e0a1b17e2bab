/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_audio_buffer.c
 * @description: 音频Buffer队列.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <stdlib.h>
#include "freertos/FreeRTOS.h"
#include "freertos/queue.h"
#include "sk_log.h"
#include "sk_os.h"
#include "sk_common.h"
#include "sk_audio_buffer.h"
#include "sk_queue.h"

#define TAG "AudioBuf"

typedef struct  {
    SkQueue_t xFreeQueue;
    SkQueue_t xDataQueue;

    SkAudioBuf *bufferArray;
    uint16_t queueSize;
    uint32_t statInFreeNull;
    uint32_t statInFreeIndexError;
    uint32_t statInFreeEqError;
    uint32_t statOutFreeIndexError;
    uint32_t statInDataNull;
    uint32_t statInDataIndexError;
    uint32_t statInDataEqError;
    uint32_t statOutDataIndexError;
} AudioQueueCtrl;

AudioQueueCtrl g_playBufferQueue;
AudioQueueCtrl g_recordBufferQueue;

/**
 * @brief 初始化音频队列
 *
 * 该函数用于初始化音频队列，创建两个队列（一个用于存储空闲缓冲区索引，一个用于存储数据缓冲区索引），
 * 并为每个缓冲区分配内存。
 *
 * @param ctrl 指向音频队列控制结构的指针
 * @param queueSize 队列大小（即缓冲区数量）
 * @param bufferSize 每个缓冲区的大小
 * @param offset 每个缓冲区的偏移量
 *
 * @return 初始化成功返回SK_RET_SUCCESS，否则返回SK_RET_FAIL
 */
int AudioQueueInit(AudioQueueCtrl *ctrl, uint32_t queueSize, uint32_t bufferSize, uint32_t offset) {
    uint32_t i;
    SkAudioBuf *bufInfo = NULL;

    SkQueueInit(&ctrl->xFreeQueue, queueSize);
    SkQueueInit(&ctrl->xDataQueue, queueSize);
    ctrl->bufferArray = (SkAudioBuf *)malloc(sizeof(SkAudioBuf) * queueSize);
    SK_LOGD(TAG, "Audio buffer array %p size is %d", ctrl->bufferArray, sizeof(SkAudioBuf) * queueSize);
    for (i = 0; i < queueSize; i++) {
        bufInfo = &ctrl->bufferArray[i];
        bufInfo->data = (uint8_t *)SkOsAllocPsram(bufferSize, sizeof(uint32_t));
        bufInfo->addrBackup = bufInfo->data;
        bufInfo->size = bufferSize;
        bufInfo->offset = offset;
        bufInfo->index = i;
        bufInfo->status = SK_AUDIO_BUF_STATE_IN_FREE;
        bufInfo->sessionId = 0;
        if (bufInfo->data == NULL) {
            SK_LOGE(TAG, "AudioQueueInit: malloc failed");
            return SK_RET_FAIL;
        }
        SK_LOGD(TAG, "Audio buffer %p size is %d", bufInfo->data, bufferSize);
        SkQueuePut(&ctrl->xFreeQueue, &bufInfo->listNode);
    }
    ctrl->queueSize = queueSize;
    return SK_RET_SUCCESS;
}

void AudioQueueDisplay(void *handler) {
    uint16_t index;
    AudioQueueCtrl *ctrl = (AudioQueueCtrl *)handler;
    SkAudioBuf *bufInfo = NULL;

    if (ctrl == NULL) {
        SK_LOGE(TAG, "AudioQueueDisplay: handler is NULL");
        return;
    }

    //SK_LOGD(TAG, "Free Queue Size %d", uxQueueSpacesAvailable(ctrl->xFreeQueue));
    SK_LOGD(TAG, "Free Queue: null %d index error %d eq error %d out index error %d",
        ctrl->statInFreeNull, ctrl->statInFreeIndexError, ctrl->statInFreeEqError,
        ctrl->statOutFreeIndexError);
    SK_LOGD(TAG, "Data Queue: null %d index error %d eq error %d out index error %d",
        ctrl->statInDataNull, ctrl->statInDataIndexError, ctrl->statInDataEqError,
        ctrl->statOutDataIndexError);
    for (index = 0; index < ctrl->queueSize; index++) {
        bufInfo = &ctrl->bufferArray[index];
        SK_LOGI(TAG, "index %d status %d session %d data %p size %d offset %d", 
            bufInfo->index, bufInfo->status, bufInfo->sessionId, bufInfo->data,
            bufInfo->size, bufInfo->offset);
    }

    return;
}

void* SkCreateAudioQueue(uint32_t queueSize, size_t bufferSize, size_t offset) {
    int32_t ret;
    AudioQueueCtrl *ctrl = (AudioQueueCtrl *)malloc(sizeof(AudioQueueCtrl));

    if (ctrl == NULL) {
        SK_LOGE(TAG, "SkCreateAudioQueue: malloc failed");
        return NULL;
    }
    ret = AudioQueueInit(ctrl, queueSize, bufferSize, offset);
    if (ret != SK_RET_SUCCESS) {
        free(ctrl);
        return NULL;
    }

    return ctrl;
}

void SkDesctoryAudioQueue(void *handler) {
    int32_t i;
    AudioQueueCtrl *ctrl = (AudioQueueCtrl *)handler;
    SkAudioBuf *bufInfo = NULL;

    if (ctrl == NULL) {
        return;
    }

    for (i = 0; i < ctrl->queueSize; i++) {
        bufInfo = &ctrl->bufferArray[i];
        free(bufInfo->data);
        bufInfo->data = NULL;
        bufInfo->addrBackup = bufInfo->data;
    }

    free(ctrl->bufferArray);
    free(ctrl);
}

#ifdef AUDIO_QUEUE_DEBUG
static bool SkAudioBufferCheck(AudioQueueCtrl *ctrl, SkAudioBuf *buf) {
    uint16_t index;

    if (buf == NULL) {
        return false;
    }

    index = buf->index;
    if (index >= ctrl->queueSize) {
        ctrl->statOutFreeIndexError++;
        SK_LOGE(TAG, "Index %d is out of range %d.", index, ctrl->queueSize);
        return false;
    }

    if (buf->data != buf->addrBackup) {
        SK_LOGE(TAG, "Data buffer error %p %p", buf->data, buf->addrBackup);
        return false;
    }

    return true;
}
#endif

SkAudioBuf* SkAudioBufferGetFree(void *handler, uint32_t timeout) {
    SkListNode_t *item = NULL;
    AudioQueueCtrl *ctrl = (AudioQueueCtrl *)handler;

    if (ctrl == NULL) {
        SK_LOGE(TAG, "SkAudioBufferGetFree: handler is NULL");
        return NULL;
    }

    item = SkQueueGet(&ctrl->xFreeQueue, timeout);
#ifdef AUDIO_QUEUE_DEBUG
    if (!SkAudioBufferCheck(ctrl, (SkAudioBuf *)item)) {
        return NULL;
    }
#endif
    if (item != NULL) {
        ((SkAudioBuf *)item)->status = SK_AUDIO_BUF_STATE_OUT_FREE;
    }

    return (SkAudioBuf *)item;
}

int SkAudioBufferPutFree(void *handler, SkAudioBuf *buf) {
    AudioQueueCtrl *ctrl = (AudioQueueCtrl *)handler;

    if (buf == NULL) {
        ctrl->statInFreeNull++;
        SK_LOGE(TAG, "SkAudioBufferPutFree: buf is NULL");
        return SK_RET_FAIL;
    }
    if (buf->index >= ctrl->queueSize) {
        ctrl->statInFreeIndexError++;
        SK_LOGE(TAG, "SkAudioBufferPutFree: index is out of range");
        return SK_RET_FAIL;
    }

    buf->status = SK_AUDIO_BUF_STATE_IN_FREE;
    SkQueuePut(&ctrl->xFreeQueue, &buf->listNode);
    return SK_RET_SUCCESS;
}

SkAudioBuf* SkAudioBufferGetData(void *handler, uint32_t timeout) {
    SkListNode_t *item = NULL;
    AudioQueueCtrl *ctrl = (AudioQueueCtrl *)handler;

    if (ctrl == NULL) {
        return NULL;
    }
    item = SkQueueGet(&ctrl->xDataQueue, timeout);
#ifdef AUDIO_QUEUE_DEBUG
    if (!SkAudioBufferCheck(ctrl, (SkAudioBuf *)item)) {
        return NULL;
    }
#endif
    if (item != NULL) {
        ((SkAudioBuf *)item)->status = SK_AUDIO_BUF_STATE_OUT_DATA;
    }

    return (SkAudioBuf *)item;
}

int SkAudioBufferPutData(void *handler, SkAudioBuf *buf) {
    AudioQueueCtrl *ctrl = (AudioQueueCtrl *)handler;

    if (buf == NULL) {
        ctrl->statInDataNull++;
        return SK_RET_FAIL;
    }
    if (buf->index >= ctrl->queueSize) {
        ctrl->statInDataIndexError++;
        return SK_RET_FAIL;
    }
    buf->status = SK_AUDIO_BUF_STATE_IN_DATA;
    SkQueuePut(&ctrl->xDataQueue, &buf->listNode);
    return SK_RET_SUCCESS;
}

bool SkAudioBufferHasFree(void *handler) {
    AudioQueueCtrl *ctrl = (AudioQueueCtrl *)handler;

    if (ctrl == NULL) {
        return false;
    }
    return ctrl->xFreeQueue.count > 0;
}

bool SkAudioBufferHasData(void *handler) {
    AudioQueueCtrl *ctrl = (AudioQueueCtrl *)handler;

    if (ctrl == NULL) {
        return false;
    }
    return ctrl->xDataQueue.count > 0;
}