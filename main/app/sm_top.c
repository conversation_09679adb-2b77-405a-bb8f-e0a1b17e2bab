/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sm_top.c
 * @description: 主状态处理, 子状态无关的处理.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <string.h>
#include "esp_timer.h" 
#include "sk_common.h"
#include "sk_log.h"
#include "sk_os.h"
#include "sk_board.h"
#include "sk_sm.h"
#include "sk_audio.h"
#include "sk_config.h"
#include "sk_wifi.h"
#include "sm.h"
#include "sk_opus.h"
#include "sk_opus_dec.h"
#include "sk_opus_enc.h"
#include "sk_clink.h"
#include "sk_rlink.h"
#include "sk_dfx.h"

#define TAG "SmTop"

#define CONFIG_SK_TEST_PERIODIC_VOICE 1
#define LOCAL_CMD_MASK (1UL << SPEECH_CMD_EVENT_VOLDOWN | \
    1UL << SPEECH_CMD_EVENT_VOLUP | 1UL << SPEECH_CMD_EVENT_CONFIG | \
    1UL << SPEECH_CMD_EVENT_START_DBG | 1UL << SPEECH_CMD_EVENT_STOP_DBG | \
    1UL << SPEECH_CMD_EVENT_SLEEP | 1UL << SPEECH_CMD_EVENT_INFO | \
    1UL << SPEECH_CMD_EVENT_MIC_ON | 1UL << SPEECH_CMD_EVENT_MIC_OFF)

typedef struct {
    int32_t state;
    uint8_t clinkState;             // 0: idle, 1: connecting, 2: connected
    uint8_t rlinkState;             // 0: idle, 1: connecting, 2: connected
    int32_t netState;               // 网络状态

    uint32_t secTime;               // 秒级计数
    int32_t runFlag;                // 状态机是否执行
    int32_t rebootCnt;              // reboot的秒级计数
    int16_t timeCnt;                // 网络连接使用的时间统计
    uint32_t cmdMask;               // 当前接受的按键掩码
    uint32_t waitWorkId;            // 等待本地提示语音的工作ID
    uint8_t otaFlag;                // 0: Unchecked, 1: Checked

    esp_timer_handle_t timer;       // 秒级定时器
    QueueHandle_t msgQueue;         // 消息队列
    SkSmItem smItemList[STATE_MAX]; // 子状态列表
    SkSpeechCmdProc *cmdProcMap;    // 按键处理映射表
    int32_t cmdProcMapLen;          // 按键处理映射表长度
    int16_t sessionId;              // 会话ID
    TaskHandle_t taskHandle;        // 状态机任务
    int32_t showCpuUsage;           // 显示CPU利用率的标志
    uint32_t taskStackBase[32];     // 任务栈的基地址
    uint16_t taskCpuCnt[32];        // 任务数量
} SkStateCtrl;

void SmConfigStartServer();
void SkSmEnterNewState(SkStateCtrl *ctrl, int32_t state, int32_t event, int32_t subEvent, int32_t param1);

SkStateCtrl g_topStateCtrl;

#ifdef CONFIG_SK_CPU_PERIODIC_SHOW
void SkSmShowCpuUsage() {  
#ifdef configUSE_TRACE_FACILITY && CONFIG_SK_CPU_USAGE_SHOW
    int32_t totalCnt = 0;
    int32_t taskCnt;
    taskCnt = SkOsGetCpuUsageArray(g_topStateCtrl.taskStackBase, g_topStateCtrl.taskCpuCnt, &totalCnt, 32);
    for (int32_t i = 0; i < taskCnt; ++i) {
        SK_LOGI(TAG, "0x%08x %d", g_topStateCtrl.taskStackBase[i], g_topStateCtrl.taskCpuCnt[i]);
    }
#else
    SK_LOGI(TAG, "configUSE_TRACE_FACILITY or CONFIG_SK_CPU_USAGE_SHOW not defined");
#endif
}
#endif

void SkSmShowSysState() {
    SkOsShowSysInfo();
    SkBspShowStat();
    SkOpusTaskShowStat();
    g_topStateCtrl.showCpuUsage = ~g_topStateCtrl.showCpuUsage;
}

void SkSmCmdDbgOn(int32_t event, int32_t subEvent, int32_t param1, int32_t param2) {
    SK_LOGI(TAG, "Debug on");
}

void SkSmVolNotice(int32_t vol) {
    uint8_t audioList[4];
    uint8_t audioLen;

    if (vol >= 100) {
        audioList[0] = AUDIO_IDX_VOICE_TO;
        audioList[1] = AUDIO_IDX_MAX;
        audioLen = 2;
    } else if (vol <= 30) {
        audioList[0] = AUDIO_IDX_VOICE_TO;
        audioList[1] = AUDIO_IDX_MIN;
        audioLen = 2;
    } else {
        audioList[0] = AUDIO_IDX_VOICE_TO;
        audioList[1] = AUDIO_IDX_0 + (vol / 10);
        audioList[2] = AUDIO_IDX_TEN;
        audioLen = 3;
    }
    SkSmPlayLocalNotice(audioList, audioLen, true);
}

int32_t SkSmSetVol(int32_t vol) {
    if (g_topStateCtrl.waitWorkId != 0) {
        SK_LOGE(TAG, "Set vol fail, prev oper %d not finished.",
            g_topStateCtrl.waitWorkId);
        return SK_RET_FAIL;
    }
    SkBspSetPlayVol(vol);
    SkSmVolNotice(vol);
    return SK_RET_SUCCESS;
}

int32_t SkSmVolUp() {
    int32_t vol = SkBspGetPlayVol();
    vol += 10;
    if (vol > 100) {
        vol = 100;
    }
    return SkSmSetVol(vol);
}

int32_t SkSmVolDown() {
    int32_t vol = SkBspGetPlayVol();
    vol -= 10;
    if (vol <= 30) {
        vol = 30;
    }
    return SkSmSetVol(vol);
}

void SkSmCmdVolUp(int32_t event, int32_t subEvent, int32_t param1, int32_t param2) {
    SkSmVolUp();
    return;
}

void SkSmCmdVolDown(int32_t event, int32_t subEvent, int32_t param1, int32_t param2) {
    SkSmVolDown();
    return;
}

void SkSmVolMax(int32_t event, int32_t subEvent, int32_t param1, int32_t param2) {
    SkSmSetVol(100);
    return;
}

void SkSmVolMin(int32_t event, int32_t subEvent, int32_t param1, int32_t param2) {
    SkSmSetVol(30);
    return;
}

void SkSmStartDbg(int32_t event, int32_t subEvent, int32_t param1, int32_t param2) {
    SkDfxLinkStart();
    return;
}

void SkSmStopDbg(int32_t event, int32_t subEvent, int32_t param1, int32_t param2) {
    SkDfxLinkStop();
    return;
}

void SkSmStartRec(int32_t event, int32_t subEvent, int32_t param1, int32_t param2) {
#if (CONFIG_PCM_DEBUG != CONFIG_PCM_DBG_CLOSE)
    SkRecorderResume();
    SkVcProcessEnable(true);    
#endif
}

void SkSmStopRec(int32_t event, int32_t subEvent, int32_t param1, int32_t param2) {
#if (CONFIG_PCM_DEBUG != CONFIG_PCM_DBG_CLOSE)
    SkVcProcessEnable(false);
#endif
}

void SkSmStartPm(int32_t event, int32_t subEvent, int32_t param1, int32_t param2) {
    SkSmEnterNewState(&g_topStateCtrl, STATE_PM, 0, 0, 0);
    return;
}

void SkSmActionDone() {
    uint8_t audioList[2];

    audioList[0] = AUDIO_IDX_DONE;
    audioList[1] = 0;
    SkSmPlayLocalNotice(audioList, 1, true);
    return;
}

void SkSmActionOk() {
    uint8_t audioList[2];

    audioList[0] = AUDIO_IDX_OK;
    audioList[1] = 0;
    SkSmPlayLocalNotice(audioList, 1, true);
    return;
}

void SkSmCmdSysInfo(int32_t event, int32_t subEvent, int32_t param1, int32_t param2) {
    SkSmShowSysState();
    SkOpusDecStat();
    SkOpusEncShowStatus();
    return;
}

SkSpeechCmdProc g_cmdProcMap[] = {
    NULL,
    SkSmStateChange,        // SPEECH_CMD_EVENT_CHAT
    SkSmStateChange,        // SPEECH_CMD_EVENT_CALL
    SkSmStateChange,        // SPEECH_CMD_EVENT_MUSIC
    SkSmStateChange,        // SPEECH_CMD_EVENT_CONFIG
    SkSmStateChange,        // SPEECH_CMD_EVENT_QUERY
    SkSmCmdVolUp,           // SPEECH_CMD_EVENT_VOLUP
    SkSmCmdVolDown,         // SPEECH_CMD_EVENT_VOLDOWN
    SkSmCmdDbgOn,           // SPEECH_CMD_EVENT_HELP
    NULL,                   // SPEECH_CMD_EVENT_PAUSE
    NULL,                   // SPEECH_CMD_EVENT_CONFIRM
    NULL,                   // SPEECH_CMD_EVENT_QUIT
    NULL,                   // SPEECH_CMD_EVENT_PREV
    NULL,                   // SPEECH_CMD_EVENT_NEXT
    NULL,                   // SPEECH_CMD_EVENT_RESUME,
    SkSmCmdSysInfo,         // SPEECH_CMD_EVENT_INFO
    SkSmVolMax,             // SPEECH_CMD_EVENT_VOLMAX
    SkSmVolMin,             // SPEECH_CMD_EVENT_VOLMIN
    SkSmStartDbg,           // SPEECH_CMD_EVENT_START_DBG
    SkSmStopDbg,            // SPEECH_CMD_EVENT_STOP_DBG
    SkSmStartPm,            // SPEECH_CMD_EVENT_SLEEP
    SkSmStateChange,        // SPEECH_CMD_EVENT_CALL_CALLEE
    SkSmStartRec,           // SPEECH_CMD_EVENT_MIC_ON
    SkSmStopRec,            // SPEECH_CMD_EVENT_MIC_OFF
};

int32_t SmCmdToState(int32_t cmd) {
    int32_t state = STATE_IDLE;

    switch (cmd) {
        case SPEECH_CMD_EVENT_CHAT:
            state = STATE_CALL;
            break;
        case SPEECH_CMD_EVENT_CALL:
        case SPEECH_CMD_EVENT_CALL_CALLEE:
            state = STATE_CALL;
            break;
        case SPEECH_CMD_EVENT_MUSIC:
            state = STATE_MUSIC;
            break;
        case SPEECH_CMD_EVENT_CONFIG:
            state = STATE_CONFIG;
            break;
        case SPEECH_CMD_EVENT_QUERY:
            state = STATE_QUERY;
            break;
        case SPEECH_CMD_EVENT_HELP:
            state = STATE_HELP;
            break;
        default:
            state = STATE_IDLE;
    }
    return state;
}

void SkSmEnterNewState(SkStateCtrl *ctrl, int32_t state, int32_t event, int32_t subEvent, int32_t param1) {
    SkSmEvent smEvent;
    SkSmItem *smItem = NULL;

    ctrl->state = state;
    SK_LOGI(TAG, "State start %d", ctrl->state);
    smItem = &ctrl->smItemList[ctrl->state];
    if (smItem->startProc != NULL) {
        smEvent.event = event;
        smEvent.subEvent = subEvent;
        smEvent.param1 = param1;
        smEvent.param2 = 0;
        smItem->startProc(&smItem->info, &smEvent);
    }

    return;
}

void SkSmStopPrev(SkStateCtrl *ctrl) {
    SkSmItem *smItem = NULL;

    if (ctrl->state >= STATE_MAX || ctrl->state < STATE_INIT) {
        return;
    }

    if (ctrl->state != STATE_IDLE) {
        SK_LOGI(TAG, "State stop %d", ctrl->state);
        smItem = &ctrl->smItemList[ctrl->state];
        if (smItem->stopProc != NULL) {
            smItem->stopProc(&smItem->info);
        }
        ctrl->state = STATE_IDLE;
        SkRledSetEvent(SK_LED_EVENT_IDLE);
    }
    return;
}

void SkSmStateChange(int32_t event, int32_t subEvent, int32_t param1, int32_t param2) {
    SkStateCtrl *ctrl = &g_topStateCtrl;
    int32_t state = SmCmdToState(subEvent);

    SkSmStopPrev(ctrl);
    SkSmEnterNewState(ctrl, state, event, subEvent, param1);

    return;
}

bool SkSmTopCmdProc(SkStateCtrl *ctrl, int32_t cmd) {
    if (ctrl->waitWorkId != 0) {
        SK_LOGE(TAG, "Wait work %d", ctrl->waitWorkId);
        return false;
    }
    if (cmd >= ctrl->cmdProcMapLen) {
        SK_LOGE(TAG, "Invalid command %d", cmd);
        return false;
    }
    if ((ctrl->cmdMask & (1UL << cmd)) == 0UL) {
        SK_LOGE(TAG, "Command %d not allowed", cmd);
        return true;
    }

    SkSpeechCmdProc cmdProc = ctrl->cmdProcMap[cmd];
    if (cmdProc != NULL) {
        SK_LOGE(TAG, "Command %d", cmd);
        cmdProc(SM_EVENT_CMD, cmd, 0, 0);
        return false;
    }
    return true;
}

int32_t SkSmGenAudioList(int32_t wifiMode, uint8_t firstIdx, uint8_t audioList[16]) {
    uint8_t ipStr[16];
    int32_t ipStrLen;

    SkWifiGetIp(wifiMode, (char *)ipStr, sizeof(ipStr));
    audioList[0] = firstIdx;
    ipStrLen = strlen((char *)ipStr);
    for (int i = 0; i < ipStrLen; i++) {
        if (ipStr[i] == '.') {
            audioList[1 + i] = AUDIO_IDX_POINT;
        } else {
            audioList[1 + i] = ipStr[i] - '0' + AUDIO_IDX_0;
        }
    }

    return 1 + ipStrLen;
}


void SmOnClinkConnectedEvent(SkStateCtrl *ctrl) {
    SkSmSendEvent((SkStateHandler)ctrl, SM_EVENT_SYSTEM, SM_EVENT_SYSTEM_ENTER_MENU, 0, 0);

    return;
}

void SmOnNetworkConnectedEvent() {
    uint8_t audioList[16];
    uint32_t listLen;

    listLen = SkSmGenAudioList(SK_WIFI_MODE_STA, AUDIO_IDX_CONNECTED, audioList);
    SkSmPlayLocalNotice(audioList, listLen, true);

    return;
}

bool SmOnNetworkEvent(SkStateCtrl *ctrl, int32_t subEvent) {
    if (subEvent == SM_EVENT_NETWORK_CONNECTING) {
        ctrl->netState = NETWORK_STATE_STA_CONNECTING;
        SK_LOGI(TAG, "Network connecting");
        SkClinkSetFunFlag(CLINK_RUN_FLAG_IDLE);
    } else if (subEvent == SM_EVENT_NETWORK_CONNECTED) {
        ctrl->netState = NETWORK_STATE_STA_CONNECTED;
        SK_LOGI(TAG, "Network connected");
        SmOnNetworkConnectedEvent();
    } else if (subEvent == SM_EVENT_NETWORK_STOP) {
        ctrl->netState = NETWORK_STATE_STA_STOP;
        SK_LOGI(TAG, "Network sta stopped");
        SkClinkSetFunFlag(CLINK_RUN_FLAG_IDLE);
    } else if (subEvent == SM_EVENT_NETWORK_SCANNING) {
        ctrl->netState = NETWORK_STATE_STA_CONNECTING;
        SK_LOGI(TAG, "Network scanning");
    } else if (subEvent == SM_EVENT_NETWORK_AP_ENABLE) {
        ctrl->netState = NETWORK_STATE_AP_ENABLE;
        SmConfigStartServer();
        SK_LOGI(TAG, "Network AP enable");
    } else if (subEvent == SM_EVENT_NETWORK_AP_STOP) {
        SK_LOGI(TAG, "Network AP stop");
        ctrl->netState = NETWORK_STATE_AP_STOP;
    }

    return false;
}

void SkSmMovedEventProc(SkStateCtrl *ctrl) {
    uint8_t audioList[2];
    if ((ctrl->state != STATE_IDLE) || (ctrl->waitWorkId == 0)) {
        return;
    }
    
    audioList[0] = AUDIO_IDX_SIU;
    SkSmPlayLocalNotice(audioList, 1, true);

    return;
}

void SkSmRledBlink(SkStateCtrl *ctrl) {
    const int8_t ledEventMap[] = {
        SK_LED_EVENT_INIT,
        SK_LED_EVENT_CONNECTING,
        SK_LED_EVENT_OTA,
        SK_LED_EVENT_IDLE,
        SK_LED_EVENT_ERROR,
        SK_LED_EVENT_CHAT,
        SK_LED_EVENT_CALLING,
        SK_LED_EVENT_MUSIC,
        SK_LED_EVENT_CONFIG,
        SK_LED_EVENT_STORY,
        SK_LED_EVENT_STORY,
        SK_LED_EVENT_STORY,
        SK_LED_EVENT_DOWN,
    };
    int32_t ledEvent = SK_LED_EVENT_IDLE;

    if (ctrl->state >= ARRAY_SIZE(ledEventMap)) {
        return;
    }
    // 后续如果有细分状态，再在这里添加处理，当前只处理几种状态
    ledEvent = ledEventMap[ctrl->state];
    // 实现闪烁效果，每隔一秒切换一次明暗
    if ((ctrl->secTime & 0x1) == 0) {
        ledEvent = SK_LED_EVENT_DOWN;
    }
    SkRledSetEvent(ledEvent);
    return;
}

bool SkSmSystemEventProc(SkStateCtrl *ctrl, SkSmEvent *event) {
    bool continueProc = false;
    uint8_t audioList[2];

    if (event->subEvent == SM_EVENT_SYSTEM_INIT_OK) {
        audioList[0] = AUDIO_IDX_START;
        SkSmPlayLocalNotice(audioList, 1, false);
        SK_LOGE(TAG, "Start Connect Network");
        SkWifiStartSta();
        ctrl->state = STATE_CONNECTING;
        ctrl->timeCnt = 0;
        SkRecorderResume();
    } else if (event->subEvent == SM_EVENT_SYSTEM_TICK) {
        ctrl->secTime++;
        SkSmRledBlink(ctrl);
        int32_t key = SkBspGetFuncKey();
        if (key == 1) {
            SkDfxLinkStart();
        } else if (key == 2) {
            SkDfxLinkStop();
        }
#if CONFIG_SK_TEST_PERIODIC_VOICE
        audioList[0] = AUDIO_IDX_0 + (ctrl->secTime % 10);
        SkSmPlayLocalNotice(audioList, 1, false);
#endif
        if (ctrl->state == STATE_REBOOT) {
            ctrl->rebootCnt++;
            if (ctrl->rebootCnt >= 10) {
                SkOsReboot();
            }
            return false;
        }
#ifdef CONFIG_SK_CPU_PERIODIC_SHOW
        if (ctrl->showCpuUsage != 0) {
            SkSmShowCpuUsage();
        }
#endif
        continueProc = true;
    } else if (event->subEvent == SM_EVENT_SYSTEM_REBOOT) {
        SK_LOGI(TAG, "System reboot request");
        SkSmStopPrev(ctrl);
        SkSmSetReboot();
        continueProc = false;
    } else if (event->subEvent == SM_EVENT_SYSTEM_USER_ACK) {
        audioList[0] = AUDIO_IDX_HERE;
        SkSmPlayLocalNotice(audioList, 1, false);
        SK_LOGE(TAG, "Action user ack");
        continueProc = false;
    } else if (event->subEvent == SM_EVENT_SYSTEM_WORK_FINISH) {
        SK_LOGD(TAG, "Work finish %u, wait work %u", event->param1, ctrl->waitWorkId);
        if (ctrl->waitWorkId <= event->param1) {
            ctrl->waitWorkId = 0;
            continueProc = true;
        }
    } else if (event->subEvent == SM_EVENT_SYSTEM_MOVED) {
        SkSmMovedEventProc(ctrl);
    } else if (event->subEvent == SM_EVENT_SYSTEM_WAKEUP) {
        if (ctrl->clinkState == 2) {
            if (ctrl->state == STATE_IDLE) {
                SkSmEnterNewState(ctrl, STATE_CALL, event->event, event->subEvent, 0);
            }
        }
        continueProc = true;
    } else if (event->subEvent == SM_EVENT_SYSTEM_ENTER_MENU) {
        SkSmEnterNewState(ctrl, STATE_CALL, event->event, event->subEvent, 0);
        continueProc = false;
    }

    return continueProc;
}

bool SkSmLinkEventProc(SkStateCtrl *ctrl, SkSmEvent *event) {
    bool continueProc = true;
    SkSmItem *smItem = NULL;

    if (event->subEvent == SM_EVENT_CLINK_CALL_REQUEST) {
        if (ctrl->state != STATE_IDLE) {
            SK_LOGI(TAG, "State is %d, reject call", ctrl->state);
        } else {
            SK_LOGI(TAG, "State start %d", STATE_CALL);
            ctrl->state = STATE_CALL;
            smItem = &ctrl->smItemList[ctrl->state];
            smItem->startProc(&smItem->info, event);   
        }
        continueProc = true;
    } else if (event->subEvent == SM_EVENT_CLINK_CONNECT) {
        SmOnClinkConnectedEvent(ctrl);
        ctrl->clinkState = SM_CLINK_STATE_CONNECTED;
        ctrl->cmdMask = 0xFFFFFFFF;
        continueProc = true;
    } else if (event->subEvent == SM_EVENT_CLINK_DISCONNECT) {
        ctrl->cmdMask = LOCAL_CMD_MASK;
        // 主控丢失消息, 在每个状态里需要用于异常退出，设置为继续处理
        ctrl->clinkState = SM_CLINK_STATE_DISCONNECT;
        continueProc = false;
    } else if (event->subEvent == SM_EVENT_CLINK_CONNECTING) {
        ctrl->clinkState = SM_CLINK_STATE_CONNECTING;
        continueProc = false;
    } else if (event->subEvent == SM_EVENT_RLINK_DISCONNECT) {
        ctrl->rlinkState = SM_RLINK_STATE_DISCONNECT;
        continueProc = true;
    } else if (event->subEvent == SM_EVENT_RLINK_CONNECT) {
        ctrl->rlinkState = SM_RLINK_STATE_CONNECTED;
        continueProc = true;
    }

    return continueProc;
}

int32_t SkSmConnectingStateProc(SkSubStateInfo *info, const SkSmEvent *event) {
    SkStateCtrl *ctrl = (SkStateCtrl *)info->privateData;
    uint8_t audioList[2];

    if (event->event == SM_EVENT_SYSTEM && event->subEvent == SM_EVENT_SYSTEM_TICK) {
        if (SkSmGetNetState() == NETWORK_STATE_STA_CONNECTED) {
            // 网络连接成功后直接进入空闲状态，不自动启动OTA
            // OTA升级只能通过按键手动触发 (FUNC_KEY2)
            SkClinkSetFunFlag(CLINK_RUN_FLAG_START);
            SkSmEnterNewState(ctrl, STATE_IDLE, 0, 0, 0);
            SK_LOGI(TAG, "Network connected, start idle (OTA disabled, use FUNC_KEY2 to trigger)");
            return SK_RET_SUCCESS;
        }
        
        ctrl->timeCnt++;
        if (ctrl->timeCnt == 60) {
            SK_LOGE(TAG, "Connect Network Timeout");
            audioList[0] = AUDIO_IDX_NEED_CFG;
            SkSmPlayLocalNotice(audioList, 1, true); 
        }
    } else if (event->event == SM_EVENT_CMD) {
        SK_LOGE(TAG, "command at connecting state %d", ctrl->timeCnt);
        if (ctrl->timeCnt < 60) {
            audioList[0] = AUDIO_IDX_CONNECTING;
        } else {
            audioList[0] = AUDIO_IDX_NEED_CFG;
        }
        SkSmPlayLocalNotice(audioList, 1, true);
    }

    return SK_RET_SUCCESS;
}

void SkSmMain(void *arg) {
    bool continueProc;
    int32_t ret;
    SkStateCtrl *ctrl = (SkStateCtrl *)arg;
    SkSmItem *smItem = NULL;
    SkSmEvent event;
    
    while (ctrl->runFlag != 0) {
        ret = xQueueReceive(ctrl->msgQueue, &event, portMAX_DELAY);
        if (ret != pdPASS) {
            SK_LOGE(TAG, "Failed to receive message");
            continue;
        }

        if (event.event != SM_EVENT_SYSTEM || event.subEvent != SM_EVENT_SYSTEM_TICK) {
            SK_LOGD(TAG, "State %d Event %d SubEvent %d param1 %d param2 %d", 
                ctrl->state, event.event, event.subEvent, event.param1, event.param2);
        }
        if (ctrl->state > STATE_MAX) {
            SK_LOGE(TAG, "Invalid state %d", ctrl->state);
            return;
        }
        
        if (event.event == SM_EVENT_CMD) {
            continueProc = SkSmTopCmdProc(ctrl, event.subEvent);
        } else if (event.event == SM_EVENT_LINK) {
            continueProc = SkSmLinkEventProc(ctrl, &event);
        } else if (event.event == SM_EVENT_NETWORK) {
            continueProc = SmOnNetworkEvent(ctrl, event.subEvent);
        } else if (event.event == SM_EVENT_SYSTEM) {
            continueProc = SkSmSystemEventProc(ctrl, &event);
        } else {
            continueProc = false;
        }
        
        if (continueProc) {
            smItem = &ctrl->smItemList[ctrl->state];
            if (smItem->eventProc != NULL) {
                smItem->eventProc(&smItem->info, &event);
            }
        }
    }
}

void SmTimerCallback(void* arg) {
    SkSmSendEvent((SkStateHandler)arg, SM_EVENT_SYSTEM, SM_EVENT_SYSTEM_TICK, 0, 0);
    return;
}

void SmStateEndCallback(SkStateHandler handler, int32_t state) {
    SkStateCtrl *ctrl = (SkStateCtrl*)handler;

    if (ctrl->state == state) {
        SK_LOGI(TAG, "State end %d", ctrl->state);
        if (ctrl->state == STATE_OTA) {
            ctrl->otaFlag = 1;
            SkClinkSetFunFlag(CLINK_RUN_FLAG_START);
        } else if (ctrl->state == STATE_PM) {
            ctrl->timeCnt = 0;
            ctrl->state = STATE_CONNECTING;
            return;    
        }
        
        SkSmEnterNewState(ctrl, STATE_IDLE, 0, 0, 0);
    }

    return;
}

SkStateHandler SkSmInit() {
    SkStateCtrl *ctrl = &g_topStateCtrl;
    const esp_timer_create_args_t timerArgs = {
        .callback = &SmTimerCallback,
        .name = "SmSecTimer",
        .arg = ctrl,
    };

    ctrl->state = STATE_IDLE;
    ctrl->msgQueue = xQueueCreate(32, sizeof(SkSmEvent));
    ctrl->cmdProcMap = g_cmdProcMap;
    ctrl->cmdProcMapLen = sizeof(g_cmdProcMap) / sizeof(SkSpeechCmdProc);
    ctrl->runFlag = 1;
    ctrl->netState = NETWORK_STATE_STA_STOP;
    ctrl->showCpuUsage = 0;
    ctrl->cmdMask = LOCAL_CMD_MASK;
    SkSmIdleInit(&ctrl->smItemList[STATE_IDLE], NULL, (SkStateHandler)ctrl);
    SkSmCallInit(&ctrl->smItemList[STATE_CALL], SmStateEndCallback, (SkStateHandler)ctrl);
    SkSmMusicInit(&ctrl->smItemList[STATE_MUSIC], SmStateEndCallback, (SkStateHandler)ctrl);
    SkSmConfigInit(&ctrl->smItemList[STATE_CONFIG], NULL, (SkStateHandler)ctrl);
    SkSmQueryInit(&ctrl->smItemList[STATE_QUERY], NULL, (SkStateHandler)ctrl);
    SkSmHelpInit(&ctrl->smItemList[STATE_HELP], NULL, (SkStateHandler)ctrl);
    SkSmConnectingInit(&ctrl->smItemList[STATE_CONNECTING], NULL, (SkStateHandler)ctrl);
    SkSmOtaInit(&ctrl->smItemList[STATE_OTA], SmStateEndCallback, (SkStateHandler)ctrl);
    SkSmPmInit(&ctrl->smItemList[STATE_PM], SmStateEndCallback, (SkStateHandler)ctrl);

    ESP_ERROR_CHECK(esp_timer_create(&timerArgs, &ctrl->timer));
    SK_LOGI(TAG, "State machine init msgQueue is %p", ctrl->msgQueue);
    // 设置定时器为周期性触发，每1秒触发一次
    ESP_ERROR_CHECK(esp_timer_start_periodic(ctrl->timer, 1000 * 1000)); // 时间单位为微秒
    xTaskCreate(SkSmMain, "SkStateMachine", 8192, (void*)&g_topStateCtrl, 5, &g_topStateCtrl.taskHandle);
    SK_LOGI(TAG, "stack base %p", pxTaskGetStackStart(g_topStateCtrl.taskHandle));
    return (SkStateHandler)ctrl;
}

void SkSmSendEvent(SkStateHandler handler, int32_t event, int32_t subEvent, uint32_t param1, uint32_t param2) {
    SkStateCtrl *ctrl = (SkStateCtrl*)handler;
    SkSmEvent smEvent;

    smEvent.event = event;
    smEvent.subEvent = subEvent;
    smEvent.param1 = param1;
    smEvent.param2 = param2;
    xQueueSend(ctrl->msgQueue, &smEvent, portMAX_DELAY);

    return;
}

void SkSmPostEvent(int32_t event, int32_t subEvent, uint32_t param1, uint32_t param2) {
    SkStateCtrl *ctrl = &g_topStateCtrl;
    SkSmEvent smEvent;

    smEvent.event = event;
    smEvent.subEvent = subEvent;
    smEvent.param1 = param1;
    smEvent.param2 = param2;
    xQueueSend(ctrl->msgQueue, &smEvent, portMAX_DELAY);
}

uint8_t SkSmGetClinkState(SkStateHandler handler) {
    SkStateCtrl *ctrl = (SkStateCtrl *)handler;
    return ctrl->clinkState;
}

uint8_t SkSmGetRlinkState(SkStateHandler handler) {
    SkStateCtrl *ctrl = (SkStateCtrl *)handler;
    return ctrl->rlinkState;
}

typedef struct {
    uint32_t idleTime;
    SkStateHandler handler;
} SmIdleCtrl;

SmIdleCtrl g_idleCtrl;

int32_t SkSmIdleStart(SkSubStateInfo *info, const SkSmEvent *event) {
    SmIdleCtrl *ctrl = (SmIdleCtrl*)info->privateData;
    ctrl->idleTime = 0;
    SK_LOGI(TAG, "Idle state start.");
    SkRecorderResume();
    SkSrProcessEnable(true);
    SkVcProcessEnable(false);
    SkRledSetEvent(SK_LED_EVENT_IDLE);
    return SK_RET_SUCCESS;
}

int32_t SkSmIdleStop(SkSubStateInfo *info) {
    return SK_RET_SUCCESS;
}

int32_t SkSmIdleEventProc(SkSubStateInfo *info, const SkSmEvent *event) {
    SmIdleCtrl *ctrl = (SmIdleCtrl*)info->privateData;
    if (event->event == SM_EVENT_SYSTEM && event->subEvent == SM_EVENT_SYSTEM_TICK) {
        ctrl->idleTime++;
        if (ctrl->idleTime == 20) {
            SK_LOGI(TAG, "Idle time %d, %d", ctrl->idleTime, SkSensorState());
            //if (SkSensorState() >= 600) {
            //SkBoardModemSetPm(true);
            //}
        }
    }
    return SK_RET_SUCCESS;
}

int16_t SkSmNewSession(SkStateHandler handler) {
    SkStateCtrl *ctrl = (SkStateCtrl*)handler;
    ctrl->sessionId++;
    if (ctrl->sessionId == 0) {
        ctrl->sessionId++;
    }
    return ctrl->sessionId;
}

int32_t SkSmIdleInit(SkSmItem *item, SkSmStateEndCallback endCb, SkStateHandler handler) {
    item->info.subState = 0;
    item->info.privateData = &g_idleCtrl;
    item->startProc = SkSmIdleStart;
    item->stopProc = SkSmIdleStop;
    item->eventProc = SkSmIdleEventProc;
    g_idleCtrl.handler = handler;

    return SK_RET_SUCCESS;
}

void SkSmOnStaConnecting(char *ssid) {
    SK_LOGI(TAG, "Connecting to %s %p", ssid, g_topStateCtrl.msgQueue);
    SkSmSendEvent(&g_topStateCtrl, SM_EVENT_NETWORK, SM_EVENT_NETWORK_CONNECTING, 0, 0);
    return;
}

void SkSmOnStaConnected(char *ssid) {
    SK_LOGI(TAG, "Connected to %s", ssid);
    SkSmSendEvent(&g_topStateCtrl, SM_EVENT_NETWORK, SM_EVENT_NETWORK_CONNECTED, 0, 0);
    return;
}

void SkSmOnStaScanBeginScan() {
    SkSmSendEvent(&g_topStateCtrl, SM_EVENT_NETWORK, SM_EVENT_NETWORK_SCANNING, 0, 0);
    return;
}

void SkSmOnStaStop() {
    SkSmSendEvent(&g_topStateCtrl, SM_EVENT_NETWORK, SM_EVENT_NETWORK_STOP, 0, 0);
    return;
}

void SkSmOnApStart() {
    SkSmSendEvent(&g_topStateCtrl, SM_EVENT_NETWORK, SM_EVENT_NETWORK_AP_ENABLE, 0, 0);
    return;
}

void SkSmOnApStop() {
    SkSmSendEvent(&g_topStateCtrl, SM_EVENT_NETWORK, SM_EVENT_NETWORK_AP_STOP, 0, 0);
    return;
}

void SkSmOnUserAck() {
    SkSmSendEvent(&g_topStateCtrl, SM_EVENT_SYSTEM, SM_EVENT_SYSTEM_USER_ACK, 0, 0);
    return;
}

void SkSmOnWorkFinish(uint32_t workId) {
    SkSmSendEvent(&g_topStateCtrl, SM_EVENT_SYSTEM, SM_EVENT_SYSTEM_WORK_FINISH, workId, 0);
    return;
}

void SkSmOnSessionDecFinish(uint16_t sessionId) {
    SkSmSendEvent(&g_topStateCtrl, SM_EVENT_LINK, SM_EVENT_RLINK_SESSION_AUDIO_END, sessionId, 0);
    return;
}

void SkSmOnWakeup() {
    SkSmSendEvent(&g_topStateCtrl, SM_EVENT_SYSTEM, SM_EVENT_SYSTEM_WAKEUP, 0, 0);
    return;
}

void SkSmOnReboot() {
    SkSmSendEvent(&g_topStateCtrl, SM_EVENT_SYSTEM, SM_EVENT_SYSTEM_REBOOT, 0, 0);
}

int32_t SkSmQueryInit(SkSmItem *item, SkSmStateEndCallback endCb, SkStateHandler handler) {
    item->info.subState = 0;
    item->info.privateData = NULL;
    item->startProc = NULL;
    item->stopProc = NULL;
    item->eventProc = NULL;
    return SK_RET_SUCCESS;
}

int32_t SkSmHelpInit(SkSmItem *item, SkSmStateEndCallback endCb, SkStateHandler handler) {
    item->info.subState = 0;
    item->info.privateData = NULL;
    item->startProc = NULL;
    item->stopProc = NULL;
    item->eventProc = NULL;
    return SK_RET_SUCCESS;
}

int32_t SkSmConnectingInit(SkSmItem *item, SkSmStateEndCallback endCb, SkStateHandler handler) {
    item->info.subState = 0;
    item->info.privateData = handler;
    item->startProc = NULL;
    item->stopProc = NULL;
    item->eventProc = SkSmConnectingStateProc;
    return SK_RET_SUCCESS;
}

int32_t SkSmGetNetState(void) {
    return g_topStateCtrl.netState;
}

void SkSmSetReboot() {
    g_topStateCtrl.state = STATE_REBOOT;
    SkWifiStop();
    SkClinkSetFunFlag(CLINK_RUN_FLAG_STOP);
    SkRlinkSetFunFlag(RLINK_TASK_STOP);
    SkRlinkEventNotify(RLINK_EVENT_STOP_CALL, 0);
    SkSrProcessEnable(false);
    SkVcProcessEnable(false);
    SkRecorderStop();
    g_topStateCtrl.rebootCnt = 0;
    return;
}

void SkSmPlayLocalNotice(uint8_t *audioList, uint8_t len, bool waitFlag) {
    uint32_t workId;

    workId = SkOpusDecPlayLocal(audioList, len);
    if (waitFlag) {
        g_topStateCtrl.waitWorkId = workId;
        SK_LOGD(TAG, "Start audio play work, id=%d", g_topStateCtrl.waitWorkId);
    }
    return;
}

bool SkSmIsPlaying(void) {
    return (g_topStateCtrl.waitWorkId != 0);
}

/**
 * @brief 手动触发OTA升级模式
 * @note 可以通过语音命令或按键调用此函数进入OTA模式
 */
void SkSmTriggerOta(void) {
    SkStateCtrl *ctrl = &g_topStateCtrl;

    SK_LOGI(TAG, "手动触发OTA升级模式");

    // 停止当前状态
    SkSmStopPrev(ctrl);

    // 强制进入OTA状态，不管otaFlag和网络状态
    SkSmEnterNewState(ctrl, STATE_OTA, 0, 0, 0);

    SK_LOGI(TAG, "已进入OTA升级模式");
}