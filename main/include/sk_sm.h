/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_sm.h
 * @description: 状态机外部接口.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#ifndef SK_SM_H
#define SK_SM_H
#include <stdbool.h>
#include <stdint.h>
#include "sk_common.h"

enum {
    SM_EVENT_CMD = 1,
    SM_EVENT_LINK = 2,
    SM_EVENT_NETWORK = 3,
    SM_EVENT_SYSTEM = 4,
};

enum {
    SPEECH_CMD_EVENT_CHAT = 1,
    SPEECH_CMD_EVENT_CALL = 2,
    SPEECH_CMD_EVENT_MUSIC = 3,
    SPEECH_CMD_EVENT_CONFIG = 4,
    SPEECH_CMD_EVENT_QUERY = 5,
    SPEECH_CMD_EVENT_VOLUP = 6,
    SPEECH_CMD_EVENT_VOLDOWN = 7,
    SPEECH_CMD_EVENT_HELP = 8,
    <PERSON>EE<PERSON>_CMD_EVENT_PAUSE = 9,
    <PERSON>EECH_CMD_EVENT_CONFIRM = 10,
    <PERSON>EE<PERSON>_CMD_EVENT_QUIT = 11,
    SPEECH_CMD_EVENT_PREV = 12,
    SPEECH_CMD_EVENT_NEXT = 13,
    SPEECH_CMD_EVENT_RESUME = 14,
    SPEECH_CMD_EVENT_INFO = 15,
    SPEECH_CMD_EVENT_VOLMAX = 16,
    SPEECH_CMD_EVENT_VOLMIN = 17,
    SPEECH_CMD_EVENT_START_DBG = 18,
    SPEECH_CMD_EVENT_STOP_DBG = 19,
    SPEECH_CMD_EVENT_SLEEP = 20,
    SPEECH_CMD_EVENT_CALL_CALLEE = 21,
    SPEECH_CMD_EVENT_MIC_ON = 22,
    SPEECH_CMD_EVENT_MIC_OFF = 23,
};

enum {
    SM_EVENT_CLINK_CONNECT = 1,
    SM_EVENT_CLINK_DISCONNECT,
    SM_EVENT_CLINK_RELAY_OK,
    SM_EVENT_CLINK_RELAY_FAIL,
    SM_EVENT_CLINK_CALL_REQUEST,
    SM_EVENT_CLINK_CONNECTING,

    SM_EVENT_RLINK_CONNECT = 20,
    SM_EVENT_RLINK_PEER_CONNECT,
    SM_EVENT_RLINK_DISCONNECT,
    SM_EVENT_RLINK_ALIGN_REPLY,
    SM_EVENT_RLINK_CALLEE_MATCH,
    SM_EVENT_RLINK_SESSION_AUDIO_END,
    SM_EVENT_RLINK_VOLUP,
    SM_EVENT_RLINK_VOLDOWN,

    SM_EVENT_SYSTEM_TICK = 40,
    SM_EVENT_SYSTEM_REBOOT,
    SM_EVENT_SYSTEM_INIT_OK,
    SM_EVENT_SYSTEM_USER_ACK,
    SM_EVENT_SYSTEM_WORK_FINISH,
    SM_EVENT_SYSTEM_MOVED,
    SM_EVENT_SYSTEM_WAKEUP,
    SM_EVENT_SYSTEM_ENTER_MENU,
    SM_EVENT_SYSTEM_START_VOICE_INPUT,
    SM_EVENT_SYSTEM_END_VOICE_INPUT,

    SM_EVENT_NETWORK_CONNECTING = 60,
    SM_EVENT_NETWORK_CONNECTED,
    SM_EVENT_NETWORK_STOP,
    SM_EVENT_NETWORK_SCANNING,
    SM_EVENT_NETWORK_AP_ENABLE,
    SM_EVENT_NETWORK_AP_STOP,
};

typedef struct {
    int32_t event;
    int32_t subEvent;
    uint32_t param1;
    uint32_t param2;
} SkSmEvent;

SkStateHandler SkSmInit();
void SkSmSendEvent(SkStateHandler handler, int32_t event, int32_t subEvent, uint32_t param1, uint32_t param2);
void SkSmPostEvent(int32_t event, int32_t subEvent, uint32_t param1, uint32_t param2);
void SkSmOnStaConnecting(char *ssid);
void SkSmOnStaConnected(char *ssid);
void SkSmOnStaScanBeginScan();
void SkSmOnStaStop();
void SkSmOnApStart();
void SkSmOnApStop();
void SkSmOnUserAck();
void SkSmOnWorkFinish(uint32_t workId);
void SkSmOnSessionDecFinish(uint16_t sessionId);
void SkSmOnWakeup();
void SkSmOnReboot();
uint32_t SkSensorState();
void SkPeripheralInit(SkStateHandler smHandler);
void SkPeripheralEnableReport(bool flag);
void SkPeripheralResetGyro(void);
void SkSmTriggerOta(void);

#endif // SK_SM_H
