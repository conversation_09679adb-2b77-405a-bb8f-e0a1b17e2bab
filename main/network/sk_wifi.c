/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_wifi.c
 * @description: Wi-Fi接口封装, 可以在AP和STA模式之间切换.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <stdlib.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/i2s_std.h"
#include "driver/gpio.h"
#include "esp_check.h"
#include "sdkconfig.h"
#include "esp_log.h"
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <errno.h>
#include <netdb.h>            // struct addrinfo
#include <arpa/inet.h>
#include "esp_netif.h"
#include "esp_timer.h" 
#include "sk_common.h"
#include "sk_sm.h"
#include "sk_wifi.h"
#include "sk_wifi_ap.h"
#include "sk_wifi_sta.h"

#define TAG "SkWifi"

typedef struct {
    QueueHandle_t msgQueue;
    SkStaHandler staHandler;
    SkApHandler apHandler;
    TaskHandle_t taskHandle;
    char ip[16];
    int32_t state;
    int32_t staState;
} SkWifiTaskCtrl;

SkWifiTaskCtrl g_wifiTaskCtrl;

void SkWifiInit() {
    SkWifiTaskCtrl *ctrl = (SkWifiTaskCtrl *)&g_wifiTaskCtrl;

    ctrl->state = SK_WIFI_MODE_NULL;
    ctrl->staHandler = SkStaInit();
    ctrl->apHandler = SkApInit();
    SkStaRegOnScanBegin(ctrl->staHandler, SkSmOnStaScanBeginScan);
    SkStaRegOnConnect(ctrl->staHandler, SkSmOnStaConnecting);
    SkStaRegOnConnected(ctrl->staHandler, SkSmOnStaConnected);

    return;
}

void SkWifiDeinit() {
    SkWifiTaskCtrl *ctrl = (SkWifiTaskCtrl *)&g_wifiTaskCtrl;

    SkStaDeinit(ctrl->staHandler);
    SkApDeinit(ctrl->apHandler);

    return;
}

int32_t SkWifiStartAp() {
    SkWifiTaskCtrl *ctrl = (SkWifiTaskCtrl *)&g_wifiTaskCtrl;

    if (ctrl->state == SK_WIFI_MODE_NULL) {
        SkApStart(ctrl->apHandler);
        ctrl->state = SK_WIFI_MODE_AP;
        SkSmOnApStart();
    }

    return SK_RET_SUCCESS;
}

int32_t SkWifiStartSta() {
    SkWifiTaskCtrl *ctrl = (SkWifiTaskCtrl *)&g_wifiTaskCtrl;

    if (ctrl->state == SK_WIFI_MODE_NULL) {
        SkStaStart(ctrl->staHandler);
        ctrl->state = SK_WIFI_MODE_STA;
    }

    return SK_RET_SUCCESS;
}

int32_t SkWifiStop() {
    SkWifiTaskCtrl *ctrl = (SkWifiTaskCtrl *)&g_wifiTaskCtrl;

    if (ctrl->state == SK_WIFI_MODE_STA) {
        SkStaStop(ctrl->staHandler);
        SkSmOnStaStop();
    } else if (ctrl->state == SK_WIFI_MODE_AP) {
        SkApStop(ctrl->apHandler);
        SkSmOnApStop();
    }
    ctrl->state = SK_WIFI_MODE_NULL;

    return SK_RET_SUCCESS;
}

void SkWifiGetIp(uint32_t wifiMode, char *ipStr, int32_t len) {
    esp_netif_ip_info_t ip_info;
    esp_netif_t *netif = esp_netif_get_handle_from_ifkey("WIFI_STA_DEF");

    if (wifiMode == SK_WIFI_MODE_STA) {
        if (netif && esp_netif_get_ip_info(netif, &ip_info) == ESP_OK) {
            sprintf(ipStr, IPSTR, IP2STR(&ip_info.ip));
        } else {
            memset(ipStr, 0, len);
        }
    } else {
        strcpy(ipStr, "***********");
    }
}