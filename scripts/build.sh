#!/bin/bash

# 定义拷贝文件的函数
# 参数1: 源文件路径
copy_to_output() {
    local source_file="$1"
    
    # 检查源文件是否存在
    if [ ! -f "$source_file" ]; then
        echo "错误：源文件 $source_file 不存在"
        return 1
    fi
    
    # 执行拷贝操作
    echo "📋 正在拷贝: $source_file -> output/"
    cp -v "$source_file" "output/"
    
    # 检查拷贝结果
    if [ $? -eq 0 ]; then
        echo "成功拷贝: $(basename "$source_file") -> output/"
        return 0
    else
        echo "拷贝失败: $source_file"
        return 2
    fi
}

# 主程序
COVERAGE_ENABLED="OFF"

# 0. 解析外部参数：-c 或 --coverage 开启
while [[ $# -gt 0 ]]; do
    case "$1" in
        -c|--coverage)
            COVERAGE_ENABLED="ON"
            shift
            ;;
          *)
            echo "未知参数: $1"
            exit 1
            ;;
    esac
done

# 1. 配置环境变量
export IDF_TOOLS_PATH=/opt/esp/espressif
. /opt/esp/esp-idf/export.sh

# 2. 编译
touch ./main/app/main.c
export MAKEFLAGS="-j 4"
if [ $COVERAGE_ENABLED == "ON" ]; then
    idf.py build -DCOVERAGE_ENABLED=ON
else
    idf.py build
fi
idf.py size

# 3. 检查/创建output目录
if [ ! -d "output" ]; then
    echo "🛠️ 创建output目录..."
    mkdir -p output || {
        echo "无法创建output目录！请检查权限"
        exit 1
    }
    echo "output目录创建成功"
else
    echo "output目录已存在"
    rm ./output/* -rf
fi

# 4. 拷贝文件
copy_to_output "./build/bootloader/bootloader.bin"
copy_to_output "./build/partition_table/partition-table.bin"
copy_to_output "./build/srmodels/srmodels.bin"
copy_to_output "./build/ota_data_initial.bin"
copy_to_output "./build/sk_terminal.bin"
copy_to_output "./build/sk_terminal.elf"

echo "build success."
