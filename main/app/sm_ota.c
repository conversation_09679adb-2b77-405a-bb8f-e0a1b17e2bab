/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sm_ota.c
 * @description: 本地OTA升级实现 - WiFi热点 + HTTP服务器
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <string.h>
#include <sys/socket.h>
#include <inttypes.h>
#include "esp_http_server.h"
#include "esp_ota_ops.h"
#include "esp_partition.h"
#include "esp_app_format.h"
#include "sk_common.h"
#include "sk_log.h"
#include "sk_audio.h"
#include "sk_sm.h"
#include "sk_wifi.h"
#include "sm.h"
#include "sk_board.h"

// 定义MIN宏（如果未定义）
#ifndef MIN
#define MIN(a, b) ((a) < (b) ? (a) : (b))
#endif

#define TAG "SmOta"
#define OTA_BUFFER_SIZE 4096
#define HTTP_SERVER_PORT 80
#define OTA_RECV_TIMEOUT_MS 180000  // 120秒超时 (2分钟)
#define MAX_FIRMWARE_SIZE (2 * 1024 * 1024)  // 最大2MB固件

typedef enum {
    OTA_STATE_IDLE = 0,
    OTA_STATE_STARTING,
    OTA_STATE_WAITING,
    OTA_STATE_RECEIVING,
    OTA_STATE_VERIFYING,
    OTA_STATE_COMPLETE,
    OTA_STATE_ERROR
} ota_state_t;

typedef struct {
    SkStateHandler handler;
    SkSmStateEndCallback endCb;
    httpd_handle_t server;
    esp_ota_handle_t ota_handle;
    const esp_partition_t *ota_partition;
    const esp_partition_t *running_partition;
    size_t received_size;
    size_t total_size;
    ota_state_t state;
    TaskHandle_t timeout_task;
    uint32_t start_time;
    bool server_running;
} SmOtaCtrl;

SmOtaCtrl g_otaSmCtrl;

// HTML上传页面
static const char upload_page[] =
"<!DOCTYPE html>"
"<html><head><title>固件升级 - SK Terminal</title>"
"<meta charset='UTF-8'>"
"<meta name='viewport' content='width=device-width, initial-scale=1.0'>"
"<style>"
"body{font-family:Arial,sans-serif;margin:0;padding:20px;background:#f5f5f5;}"
".container{max-width:600px;margin:0 auto;background:white;padding:30px;border-radius:10px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}"
"h1{color:#333;text-align:center;margin-bottom:30px;}"
".device-info{background:#e9ecef;padding:15px;border-radius:5px;margin-bottom:20px;}"
".upload-area{border:2px dashed #ccc;padding:40px;text-align:center;margin:20px 0;border-radius:5px;transition:border-color 0.3s;}"
".upload-area:hover{border-color:#007bff;}"
".upload-area.dragover{border-color:#007bff;background:#f8f9fa;}"
"input[type=file]{margin:20px 0;padding:10px;border:1px solid #ddd;border-radius:5px;width:100%;box-sizing:border-box;}"
"button{background:#007bff;color:white;padding:12px 30px;border:none;border-radius:5px;cursor:pointer;font-size:16px;margin:5px;}"
"button:hover{background:#0056b3;}"
"button:disabled{background:#ccc;cursor:not-allowed;}"
".progress{width:100%;height:25px;background:#f0f0f0;border-radius:12px;margin:20px 0;display:none;overflow:hidden;}"
".progress-bar{height:100%;background:linear-gradient(90deg,#28a745,#20c997);border-radius:12px;width:0%;transition:width 0.3s;text-align:center;line-height:25px;color:white;font-weight:bold;}"
".status{margin:20px 0;padding:15px;border-radius:5px;display:none;}"
".success{background:#d4edda;color:#155724;border:1px solid #c3e6cb;}"
".error{background:#f8d7da;color:#721c24;border:1px solid #f5c6cb;}"
".info{background:#d1ecf1;color:#0c5460;border:1px solid #bee5eb;}"
".warning{background:#fff3cd;color:#856404;border:1px solid #ffeaa7;}"
".file-info{margin:10px 0;padding:10px;background:#f8f9fa;border-radius:5px;display:none;}"
".btn-group{text-align:center;margin-top:20px;}"
"</style></head>"
"<body><div class='container'>"
"<h1>🚀 SK Terminal 固件升级</h1>"
"<div class='device-info'>"
"<strong>设备信息:</strong><br>"
"IP地址: ***********<br>"
"当前版本: <span id='currentVersion'>获取中...</span><br>"
"运行分区: <span id='runningPartition'>获取中...</span>"
"</div>"
"<div class='upload-area' id='uploadArea'>"
"<p>📁 拖拽固件文件到此处或点击选择</p>"
"<input type='file' id='firmware' accept='.bin' onchange='selectFile(this)'>"
"</div>"
"<div class='file-info' id='fileInfo'></div>"
"<div class='btn-group'>"
"<button onclick='uploadFirmware()' id='uploadBtn' disabled>开始升级</button>"
"<button onclick='checkStatus()' id='statusBtn'>检查状态</button>"
"<button onclick='location.reload()' id='refreshBtn'>刷新页面</button>"
"</div>"
"<div class='progress' id='progress'><div class='progress-bar' id='progressBar'>0%</div></div>"
"<div class='status' id='status'></div>"
"</div>"
"<script>"
"let selectedFile=null;"
"let uploadInProgress=false;"
"function selectFile(input){"
"selectedFile=input.files[0];"
"updateFileInfo();"
"document.getElementById('uploadBtn').disabled=!selectedFile||uploadInProgress;"
"}"
"function updateFileInfo(){"
"const fileInfo=document.getElementById('fileInfo');"
"if(selectedFile){"
"fileInfo.innerHTML='<strong>选中文件:</strong> '+selectedFile.name+'<br><strong>大小:</strong> '+(selectedFile.size/1024/1024).toFixed(2)+' MB';"
"fileInfo.style.display='block';"
"}else{"
"fileInfo.style.display='none';"
"}}"
"function showStatus(msg,type){"
"const status=document.getElementById('status');"
"status.innerHTML=msg;"
"status.className='status '+type;"
"status.style.display='block';"
"}"
"function updateProgress(percent,text){"
"const progress=document.getElementById('progress');"
"const progressBar=document.getElementById('progressBar');"
"progress.style.display='block';"
"progressBar.style.width=percent+'%';"
"progressBar.textContent=text||percent+'%';"
"}"
"function uploadFirmware(){"
"if(!selectedFile||uploadInProgress){showStatus('请选择固件文件','error');return;}"
"if(selectedFile.size>2097152){showStatus('固件文件过大，最大支持2MB','error');return;}"
"uploadInProgress=true;"
"document.getElementById('uploadBtn').disabled=true;"
"const formData=new FormData();"
"formData.append('firmware',selectedFile);"
"const xhr=new XMLHttpRequest();"
"xhr.upload.onprogress=function(e){"
"if(e.lengthComputable){"
"const percent=Math.round((e.loaded/e.total)*100);"
"updateProgress(percent,'上传: '+percent+'%');"
"showStatus('正在上传固件: '+percent+'%','info');"
"}};"
"xhr.onload=function(){"
"if(xhr.status===200){"
"showStatus('固件上传成功，正在验证和安装...','success');"
"updateProgress(100,'验证中...');"
"setTimeout(checkStatus,2000);"
"}else{"
"showStatus('上传失败: '+xhr.responseText,'error');"
"uploadInProgress=false;"
"document.getElementById('uploadBtn').disabled=false;"
"}};"
"xhr.onerror=function(){"
"showStatus('网络错误，请检查连接后重试','error');"
"uploadInProgress=false;"
"document.getElementById('uploadBtn').disabled=false;"
"};"
"xhr.open('POST','/upload');"
"xhr.send(formData);"
"}"
"function checkStatus(){"
"fetch('/status').then(r=>r.json()).then(data=>{"
"if(data.status==='complete'){"
"showStatus('🎉 升级完成！设备将在5秒后重启到新固件...','success');"
"updateProgress(100,'升级完成');"
"setTimeout(()=>{"
"showStatus('正在重启设备...','info');"
"fetch('/reboot',{method:'POST'});"
"},5000);"
"}else if(data.status==='error'){"
"showStatus('❌ 升级失败: '+data.message,'error');"
"uploadInProgress=false;"
"document.getElementById('uploadBtn').disabled=false;"
"}else if(data.status==='receiving'){"
"updateProgress(data.percent,'接收: '+data.percent+'%');"
"showStatus('正在接收固件数据: '+data.percent+'%','info');"
"setTimeout(checkStatus,1000);"
"}else if(data.status==='verifying'){"
"updateProgress(95,'验证中...');"
"showStatus('正在验证固件完整性...','info');"
"setTimeout(checkStatus,1000);"
"}else{"
"showStatus(data.message||'等待操作...','info');"
"setTimeout(checkStatus,2000);"
"}}).catch(()=>setTimeout(checkStatus,2000));"
"}"
"// 拖拽上传支持"
"const uploadArea=document.getElementById('uploadArea');"
"uploadArea.addEventListener('dragover',function(e){"
"e.preventDefault();"
"uploadArea.classList.add('dragover');"
"});"
"uploadArea.addEventListener('dragleave',function(e){"
"uploadArea.classList.remove('dragover');"
"});"
"uploadArea.addEventListener('drop',function(e){"
"e.preventDefault();"
"uploadArea.classList.remove('dragover');"
"const files=e.dataTransfer.files;"
"if(files.length>0){"
"document.getElementById('firmware').files=files;"
"selectFile(document.getElementById('firmware'));"
"}});"
"// 页面加载时获取设备信息"
"fetch('/info').then(r=>r.json()).then(data=>{"
"document.getElementById('currentVersion').textContent=data.version||'未知';"
"document.getElementById('runningPartition').textContent=data.partition||'未知';"
"}).catch(()=>{"
"document.getElementById('currentVersion').textContent='获取失败';"
"document.getElementById('runningPartition').textContent='获取失败';"
"});"
"</script></body></html>";

// HTTP处理函数：主页
static esp_err_t upload_page_handler(httpd_req_t *req) {
    SK_LOGI(TAG, "客户端访问上传页面");
    httpd_resp_set_type(req, "text/html; charset=utf-8");
    httpd_resp_set_hdr(req, "Cache-Control", "no-cache");
    httpd_resp_send(req, upload_page, strlen(upload_page));
    return ESP_OK;
}

// HTTP处理函数：设备信息
static esp_err_t info_handler(httpd_req_t *req) {
    SmOtaCtrl *ctrl = &g_otaSmCtrl;
    char response[512];

    const esp_app_desc_t *app_desc = esp_app_get_description();

    snprintf(response, sizeof(response),
        "{"
        "\"version\":\"%s\","
        "\"partition\":\"%s\","
        "\"compile_time\":\"%s %s\","
        "\"idf_version\":\"%s\","
        "\"state\":\"%s\""
        "}",
        app_desc->version,
        ctrl->running_partition ? ctrl->running_partition->label : "unknown",
        app_desc->date, app_desc->time,
        app_desc->idf_ver,
        ctrl->state == OTA_STATE_WAITING ? "waiting" :
        ctrl->state == OTA_STATE_RECEIVING ? "receiving" :
        ctrl->state == OTA_STATE_VERIFYING ? "verifying" :
        ctrl->state == OTA_STATE_COMPLETE ? "complete" :
        ctrl->state == OTA_STATE_ERROR ? "error" : "idle"
    );

    httpd_resp_set_type(req, "application/json");
    httpd_resp_send(req, response, strlen(response));
    return ESP_OK;
}

// 固件验证函数
static esp_err_t verify_firmware_header(const uint8_t *data, size_t len) {
    if (len < sizeof(esp_image_header_t)) {
        SK_LOGE(TAG, "固件数据太小，无法包含有效头部");
        return ESP_FAIL;
    }

    const esp_image_header_t *header = (const esp_image_header_t *)data;

    // 检查魔数
    if (header->magic != ESP_IMAGE_HEADER_MAGIC) {
        SK_LOGE(TAG, "固件魔数错误: 0x%02x (期望: 0x%02x)",
                header->magic, ESP_IMAGE_HEADER_MAGIC);
        return ESP_FAIL;
    }

    // 检查芯片类型
    if (header->chip_id != ESP_CHIP_ID_ESP32S3) {
        SK_LOGE(TAG, "固件芯片类型不匹配: %d (期望: %d)",
                header->chip_id, ESP_CHIP_ID_ESP32S3);
        return ESP_FAIL;
    }

    SK_LOGI(TAG, "固件头部验证通过 - 芯片: ESP32S3, 段数: %d", header->segment_count);
    return ESP_OK;
}

// 简化的multipart解析 - 查找ESP32固件魔数
static esp_err_t parse_multipart_firmware(httpd_req_t *req, uint8_t **firmware_data, size_t *firmware_size) {
    SK_LOGI(TAG, "使用简化的固件提取方法");

    // 读取所有数据到缓冲区
    uint8_t *full_data = malloc(req->content_len);
    if (!full_data) {
        SK_LOGE(TAG, "分配内存失败");
        return ESP_FAIL;
    }

    int total_received = 0;
    while (total_received < req->content_len) {
        int received = httpd_req_recv(req, (char*)full_data + total_received,
                                     req->content_len - total_received);
        if (received <= 0) {
            SK_LOGE(TAG, "接收数据失败: %d", received);
            free(full_data);
            return ESP_FAIL;
        }
        total_received += received;
    }

    SK_LOGI(TAG, "接收到完整数据: %d bytes", total_received);

    // 查找ESP32固件魔数 0xe9 的位置
    uint8_t *firmware_start = NULL;
    for (int i = 0; i < req->content_len - 4; i++) {
        if (full_data[i] == 0xe9 && full_data[i+1] == 0x06) {
            // 找到可能的ESP32固件头部
            firmware_start = full_data + i;
            SK_LOGI(TAG, "找到ESP32固件魔数在偏移: %d", i);
            break;
        }
    }

    if (!firmware_start) {
        SK_LOGE(TAG, "未找到ESP32固件魔数 0xe9");
        // 打印前100字节用于调试
        SK_LOGE(TAG, "数据开头hex: ");
        for (int i = 0; i < MIN(100, req->content_len); i++) {
            printf("%02x ", full_data[i]);
            if ((i + 1) % 16 == 0) printf("\n");
        }
        printf("\n");
        free(full_data);
        return ESP_FAIL;
    }

    // 计算固件大小 - 从魔数位置到数据末尾，减去可能的multipart尾部
    size_t remaining_size = req->content_len - (firmware_start - full_data);

    // 从末尾向前查找，跳过可能的multipart结束标记
    uint8_t *firmware_end = full_data + req->content_len;

    // 向前查找，跳过非固件数据（通常是ASCII字符）
    while (firmware_end > firmware_start + 1000) { // 至少保留1KB的固件数据
        firmware_end--;
        // 如果遇到连续的ASCII字符（如boundary），说明固件数据结束了
        if (*firmware_end < 0x20 || *firmware_end > 0x7E) {
            // 这可能是二进制数据，继续
            continue;
        }
        // 检查是否是连续的ASCII字符（可能是boundary）
        bool is_ascii_sequence = true;
        for (int j = 0; j < 10 && firmware_end + j < full_data + req->content_len; j++) {
            uint8_t c = firmware_end[j];
            if (c < 0x20 && c != '\r' && c != '\n') {
                is_ascii_sequence = false;
                break;
            }
        }
        if (is_ascii_sequence) {
            // 找到了ASCII序列，可能是boundary，在这里截断
            break;
        }
    }

    *firmware_size = firmware_end - firmware_start;

    SK_LOGI(TAG, "计算固件大小: %d bytes (开始偏移: %d, 结束偏移: %d)",
            *firmware_size, (int)(firmware_start - full_data), (int)(firmware_end - full_data));

    if (*firmware_size <= 0 || *firmware_size > MAX_FIRMWARE_SIZE) {
        SK_LOGE(TAG, "固件大小异常: %d", *firmware_size);
        free(full_data);
        return ESP_FAIL;
    }

    *firmware_data = malloc(*firmware_size);
    if (!*firmware_data) {
        SK_LOGE(TAG, "分配固件缓冲区失败");
        free(full_data);
        return ESP_FAIL;
    }

    memcpy(*firmware_data, firmware_start, *firmware_size);

    free(full_data);

    SK_LOGI(TAG, "固件提取成功，大小: %d bytes", *firmware_size);
    return ESP_OK;
}


// HTTP处理函数：固件上传
static esp_err_t upload_firmware_handler(httpd_req_t *req) {
    SmOtaCtrl *ctrl = &g_otaSmCtrl;
    uint8_t *firmware_data = NULL;
    size_t firmware_size = 0;
    esp_err_t err = ESP_OK;

    SK_LOGI(TAG, "开始接收固件，大小: %d bytes", req->content_len);

    // 解析multipart数据
    if (parse_multipart_firmware(req, &firmware_data, &firmware_size) != ESP_OK) {
        SK_LOGE(TAG, "解析multipart数据失败");
        httpd_resp_send_err(req, HTTPD_400_BAD_REQUEST, "数据格式错误");
        return ESP_FAIL;
    }

    // 验证固件大小
    if (firmware_size <= 0 || firmware_size > MAX_FIRMWARE_SIZE) {
        SK_LOGE(TAG, "固件大小无效: %d", firmware_size);
        free(firmware_data);
        httpd_resp_send_err(req, HTTPD_400_BAD_REQUEST, "固件大小无效 (最大2MB)");
        return ESP_FAIL;
    }

    ctrl->state = OTA_STATE_RECEIVING;
    ctrl->total_size = firmware_size;
    ctrl->received_size = 0;
    ctrl->start_time = xTaskGetTickCount();

    // 验证固件头部
    if (verify_firmware_header(firmware_data, firmware_size) != ESP_OK) {
        SK_LOGE(TAG, "固件头部验证失败");
        free(firmware_data);
        ctrl->state = OTA_STATE_ERROR;
        httpd_resp_send_err(req, HTTPD_400_BAD_REQUEST, "固件格式错误");
        return ESP_FAIL;
    }

    // 获取下一个OTA分区
    ctrl->ota_partition = esp_ota_get_next_update_partition(NULL);
    if (!ctrl->ota_partition) {
        SK_LOGE(TAG, "无法获取OTA分区");
        free(firmware_data);
        ctrl->state = OTA_STATE_ERROR;
        httpd_resp_send_err(req, HTTPD_500_INTERNAL_SERVER_ERROR, "OTA分区错误");
        return ESP_FAIL;
    }

    SK_LOGI(TAG, "目标OTA分区: %s, 地址: 0x%x, 大小: %d",
            ctrl->ota_partition->label, ctrl->ota_partition->address, ctrl->ota_partition->size);

    // 开始OTA会话
    err = esp_ota_begin(ctrl->ota_partition, firmware_size, &ctrl->ota_handle);
    if (err != ESP_OK) {
        SK_LOGE(TAG, "esp_ota_begin失败: %s", esp_err_to_name(err));
        free(firmware_data);
        ctrl->state = OTA_STATE_ERROR;
        httpd_resp_send_err(req, HTTPD_500_INTERNAL_SERVER_ERROR, "OTA初始化失败");
        return ESP_FAIL;
    }

    // 分块写入固件数据到Flash
    size_t written = 0;
    const size_t chunk_size = 4096; // 4KB chunks

    while (written < firmware_size) {
        size_t write_size = MIN(chunk_size, firmware_size - written);

        // 写入固件数据到Flash
        err = esp_ota_write(ctrl->ota_handle, firmware_data + written, write_size);
        if (err != ESP_OK) {
            SK_LOGE(TAG, "esp_ota_write失败: %s", esp_err_to_name(err));
            esp_ota_abort(ctrl->ota_handle);
            free(firmware_data);
            ctrl->state = OTA_STATE_ERROR;
            httpd_resp_send_err(req, HTTPD_500_INTERNAL_SERVER_ERROR, "写入Flash失败");
            return ESP_FAIL;
        }

        written += write_size;
        ctrl->received_size = written;

        // 定期打印进度
        if (written % (128 * 1024) == 0 || written == firmware_size) {
            uint32_t percent = (written * 100) / firmware_size;
            uint32_t elapsed = xTaskGetTickCount() - ctrl->start_time;
            uint32_t speed = elapsed > 0 ? (written / elapsed) : 0;
            SK_LOGI(TAG, "写入进度: %d/%d (%d%%) - 速度: %d KB/s",
                    written, firmware_size, percent, speed);
        }
    }

    // 释放固件数据缓冲区
    free(firmware_data);

    SK_LOGI(TAG, "固件接收完成，开始验证");
    ctrl->state = OTA_STATE_VERIFYING;
    
    // 完成OTA写入和验证
    err = esp_ota_end(ctrl->ota_handle);
    if (err != ESP_OK) {
        SK_LOGE(TAG, "esp_ota_end失败: %s", esp_err_to_name(err));
        ctrl->state = OTA_STATE_ERROR;
        httpd_resp_send_err(req, HTTPD_500_INTERNAL_SERVER_ERROR, "固件验证失败");
        return ESP_FAIL;
    }

    // 设置新的启动分区
    err = esp_ota_set_boot_partition(ctrl->ota_partition);
    if (err != ESP_OK) {
        SK_LOGE(TAG, "esp_ota_set_boot_partition失败: %s", esp_err_to_name(err));
        ctrl->state = OTA_STATE_ERROR;
        httpd_resp_send_err(req, HTTPD_500_INTERNAL_SERVER_ERROR, "设置启动分区失败");
        return ESP_FAIL;
    }

    uint32_t total_time = xTaskGetTickCount() - ctrl->start_time;
    SK_LOGI(TAG, "OTA升级成功完成！总耗时: %d ms, 平均速度: %d KB/s",
            total_time, total_time > 0 ? (firmware_size / total_time) : 0);

    ctrl->state = OTA_STATE_COMPLETE;
    httpd_resp_send(req, "OK", 2);
    return ESP_OK;
}

// HTTP处理函数：状态查询
static esp_err_t status_handler(httpd_req_t *req) {
    SmOtaCtrl *ctrl = &g_otaSmCtrl;
    char response[512];

    httpd_resp_set_type(req, "application/json");

    switch (ctrl->state) {
        case OTA_STATE_WAITING:
            snprintf(response, sizeof(response),
                "{\"status\":\"waiting\",\"message\":\"等待固件上传\"}");
            break;
        case OTA_STATE_RECEIVING:
            if (ctrl->total_size > 0) {
                uint32_t percent = (ctrl->received_size * 100) / ctrl->total_size;
                uint32_t elapsed = xTaskGetTickCount() - ctrl->start_time;
                uint32_t speed = elapsed > 0 ? (ctrl->received_size / elapsed) : 0;
                snprintf(response, sizeof(response),
                    "{\"status\":\"receiving\",\"progress\":%zu,\"total\":%zu,\"percent\":%"PRIu32",\"speed\":%"PRIu32"}",
                    ctrl->received_size, ctrl->total_size, percent, speed);
            } else {
                snprintf(response, sizeof(response),
                    "{\"status\":\"receiving\",\"message\":\"正在接收固件数据\"}");
            }
            break;
        case OTA_STATE_VERIFYING:
            snprintf(response, sizeof(response),
                "{\"status\":\"verifying\",\"message\":\"正在验证固件完整性\"}");
            break;
        case OTA_STATE_COMPLETE:
            snprintf(response, sizeof(response),
                "{\"status\":\"complete\",\"message\":\"升级完成，准备重启\"}");
            break;
        case OTA_STATE_ERROR:
            snprintf(response, sizeof(response),
                "{\"status\":\"error\",\"message\":\"升级失败，请重试\"}");
            break;
        default:
            snprintf(response, sizeof(response),
                "{\"status\":\"idle\",\"message\":\"OTA服务就绪\"}");
            break;
    }

    httpd_resp_send(req, response, strlen(response));
    return ESP_OK;
}

// HTTP处理函数：重启设备
static esp_err_t reboot_handler(httpd_req_t *req) {
    SK_LOGI(TAG, "收到重启请求");

    char response[] = "{\"status\":\"rebooting\",\"message\":\"设备重启中...\"}";
    httpd_resp_set_type(req, "application/json");
    httpd_resp_send(req, response, strlen(response));

    // 延迟重启，确保响应发送完成
    vTaskDelay(pdMS_TO_TICKS(1000));
    esp_restart();

    return ESP_OK;
}

// 超时任务 - 防止长时间占用AP模式
static void ota_timeout_task(void *arg) {
    SmOtaCtrl *ctrl = (SmOtaCtrl *)arg;

    SK_LOGI(TAG, "OTA超时任务启动，%d分钟后自动退出", OTA_RECV_TIMEOUT_MS / 60000);
    vTaskDelay(pdMS_TO_TICKS(OTA_RECV_TIMEOUT_MS));

    if (ctrl->state == OTA_STATE_WAITING) {
        SK_LOGW(TAG, "OTA超时，自动退出升级模式");
        ctrl->state = OTA_STATE_ERROR;
        if (ctrl->endCb) {
            ctrl->endCb(ctrl->handler, STATE_OTA);
        }
    }

    ctrl->timeout_task = NULL;
    vTaskDelete(NULL);
}

// 启动HTTP服务器
static esp_err_t start_ota_server(SmOtaCtrl *ctrl) {
    httpd_config_t config = HTTPD_DEFAULT_CONFIG();
    config.server_port = HTTP_SERVER_PORT;
    config.max_uri_handlers = 8;
    config.max_resp_headers = 8;
    config.stack_size = 8192;
    config.recv_wait_timeout = 30;  // 30秒接收超时
    config.send_wait_timeout = 10;  // 10秒发送超时

    SK_LOGI(TAG, "启动HTTP服务器，端口: %d", config.server_port);

    if (httpd_start(&ctrl->server, &config) != ESP_OK) {
        SK_LOGE(TAG, "启动HTTP服务器失败");
        return ESP_FAIL;
    }

    // 注册URI处理器

    // 主页 - GET /
    httpd_uri_t upload_page_uri = {
        .uri = "/",
        .method = HTTP_GET,
        .handler = upload_page_handler,
        .user_ctx = NULL
    };
    httpd_register_uri_handler(ctrl->server, &upload_page_uri);

    // 设备信息 - GET /info
    httpd_uri_t info_uri = {
        .uri = "/info",
        .method = HTTP_GET,
        .handler = info_handler,
        .user_ctx = NULL
    };
    httpd_register_uri_handler(ctrl->server, &info_uri);

    // 固件上传 - POST /upload
    httpd_uri_t upload_uri = {
        .uri = "/upload",
        .method = HTTP_POST,
        .handler = upload_firmware_handler,
        .user_ctx = NULL
    };
    httpd_register_uri_handler(ctrl->server, &upload_uri);

    // 状态查询 - GET /status
    httpd_uri_t status_uri = {
        .uri = "/status",
        .method = HTTP_GET,
        .handler = status_handler,
        .user_ctx = NULL
    };
    httpd_register_uri_handler(ctrl->server, &status_uri);

    // 重启设备 - POST /reboot
    httpd_uri_t reboot_uri = {
        .uri = "/reboot",
        .method = HTTP_POST,
        .handler = reboot_handler,
        .user_ctx = NULL
    };
    httpd_register_uri_handler(ctrl->server, &reboot_uri);

    ctrl->server_running = true;
    SK_LOGI(TAG, "HTTP服务器启动成功，可访问: http://***********");

    return ESP_OK;
}

// 停止HTTP服务器
static void stop_ota_server(SmOtaCtrl *ctrl) {
    if (ctrl->server && ctrl->server_running) {
        SK_LOGI(TAG, "停止HTTP服务器");
        httpd_stop(ctrl->server);
        ctrl->server = NULL;
        ctrl->server_running = false;
    }
}

// 清理OTA资源
static void cleanup_ota_resources(SmOtaCtrl *ctrl) {
    // 中止OTA操作
    if (ctrl->ota_handle) {
        esp_ota_abort(ctrl->ota_handle);
        ctrl->ota_handle = 0;
    }

    // 停止HTTP服务器
    stop_ota_server(ctrl);

    // 停止超时任务
    if (ctrl->timeout_task) {
        vTaskDelete(ctrl->timeout_task);
        ctrl->timeout_task = NULL;
    }

    // 重置状态
    ctrl->state = OTA_STATE_IDLE;
    ctrl->received_size = 0;
    ctrl->total_size = 0;
}

// OTA状态机实现
int32_t SmOtaStart(SkSubStateInfo *info, const SkSmEvent *event) {
    SmOtaCtrl *ctrl = (SmOtaCtrl *)info->privateData;

    SK_LOGI(TAG, "启动本地OTA升级模式");
    SkRledSetEvent(SK_LED_EVENT_OTA);

    // 初始化状态
    ctrl->state = OTA_STATE_STARTING;
    ctrl->received_size = 0;
    ctrl->total_size = 0;
    ctrl->ota_handle = 0;
    ctrl->start_time = 0;

    // 获取当前运行分区信息
    ctrl->running_partition = esp_ota_get_running_partition();
    if (ctrl->running_partition) {
        SK_LOGI(TAG, "当前运行分区: %s", ctrl->running_partition->label);
    }

    // 启动WiFi AP模式
    SK_LOGI(TAG, "启动WiFi AP模式...");
    if (SkWifiStartAp() != SK_RET_SUCCESS) {
        SK_LOGE(TAG, "启动WiFi AP失败");
        ctrl->state = OTA_STATE_ERROR;
        return SK_RET_FAIL;
    }

    // 等待AP启动完成
    vTaskDelay(pdMS_TO_TICKS(3000));

    // 启动HTTP服务器
    if (start_ota_server(ctrl) != ESP_OK) {
        SK_LOGE(TAG, "启动HTTP服务器失败");
        SkWifiStop();
        ctrl->state = OTA_STATE_ERROR;
        return SK_RET_FAIL;
    }

    ctrl->state = OTA_STATE_WAITING;

    // 启动超时任务
    if (xTaskCreate(ota_timeout_task, "ota_timeout", 2048, ctrl, 3, &ctrl->timeout_task) != pdPASS) {
        SK_LOGW(TAG, "创建超时任务失败");
    }

    return SK_RET_SUCCESS;
}

int32_t SmOtaStop(SkSubStateInfo *info) {
    SmOtaCtrl *ctrl = (SmOtaCtrl *)info->privateData;

    SK_LOGI(TAG, "停止OTA升级模式");

    // 清理所有OTA资源
    cleanup_ota_resources(ctrl);

    // 停止WiFi AP模式
    SkWifiStop();

    SK_LOGI(TAG, "OTA升级模式已停止");

    return SK_RET_SUCCESS;
}

int32_t SmOtaEventProc(SkSubStateInfo *info, const SkSmEvent *event) {
    SmOtaCtrl *ctrl = (SmOtaCtrl *)info->privateData;

    // 检查是否需要结束OTA状态
    if (ctrl->state == OTA_STATE_COMPLETE || ctrl->state == OTA_STATE_ERROR) {
        if (ctrl->endCb != NULL) {
            SK_LOGI(TAG, "OTA状态结束，状态: %s",
                    ctrl->state == OTA_STATE_COMPLETE ? "完成" : "错误");
            ctrl->endCb(ctrl->handler, STATE_OTA);
        }
    }

    return SK_RET_SUCCESS;
}

int32_t SkSmOtaInit(SkSmItem *item, SkSmStateEndCallback endCb, SkStateHandler handler) {
    SmOtaCtrl *ctrl = &g_otaSmCtrl;

    // 初始化控制结构
    memset(ctrl, 0, sizeof(SmOtaCtrl));
    ctrl->endCb = endCb;
    ctrl->handler = handler;
    ctrl->server = NULL;
    ctrl->server_running = false;
    ctrl->state = OTA_STATE_IDLE;
    ctrl->ota_handle = 0;
    ctrl->timeout_task = NULL;

    item->info.subState = 0;
    item->info.privateData = ctrl;
    item->startProc = SmOtaStart;
    item->stopProc = SmOtaStop;
    item->eventProc = SmOtaEventProc;

    SK_LOGI(TAG, "本地OTA模块初始化完成");
    SK_LOGI(TAG, "支持功能: WiFi热点 + HTTP服务器 + 固件验证 + 自动重启");

    return SK_RET_SUCCESS;
}
