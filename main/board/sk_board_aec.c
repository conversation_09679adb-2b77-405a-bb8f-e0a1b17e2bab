/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_board_aec.c
 * @description: 板级驱动接口适配.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <esp_err.h>
#include <esp_log.h>
#include <esp_sleep.h>
#include <esp_pm.h>
#include <esp_wifi.h>
#include <esp_mac.h>
#include <driver/i2c_master.h>
#include <driver/rtc_io.h>
#include <nvs_flash.h>
#include "sk_board_def.h"
#include "sk_aec_board.h"
#include "sk_board.h"
#include "sk_common.h"
#include "sk_os.h"
#include "audio_8311_7210.h"

#define TAG "SkBsp"
#define ADC_I2S_CHANNEL 2
#define MAX_PLAY_SAMPLE_PER_CALL 800*2

typedef struct {
    i2c_master_bus_handle_t i2cBus;
    i2c_master_bus_handle_t i2cBusSensor;
} SkBoard;

int32_t SkBspI2cInit(SkBoard *board);
int32_t SkGyroInit(i2c_master_bus_handle_t i2cBus);
int32_t SkTHSInit(i2c_master_bus_handle_t i2cBus);

SkBoard g_skBoard;

sk_err_t SkBspBoardInit(uint32_t sampleRate, int bitsPerChan) {
    SkBoard *board = &g_skBoard;

    ESP_LOGE(TAG, BOARD_TYPE_NAME);
    // 系统默认的事件循环
    ESP_ERROR_CHECK(esp_event_loop_create_default());
    // Initialize NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    SK_OS_MODULE_MEM_STAT("flash", true);

    // GPIO是最简单的, 没有依赖，先初始化
    SkBspKeyInit();
    SkBspVibrationGpioInit();
    SkBspHeadPhoneGpioInit();
    ESP_LOGI(TAG, "GPIO Init succ");

    // 初始化ADC设备
    SkBspAdcInit();
    ESP_LOGI(TAG, "ADC Init succ");
    
    // I2C用于音频芯片控制，在音频之前初始化
    SkBspI2cInit(board);
    ESP_LOGI(TAG, "I2C Init succ");
    AudioDevInit(board->i2cBus, sampleRate, bitsPerChan);
    ESP_LOGI(TAG, "Audio Dev Init succ");
    AudioDevEnableMic(true);

    return SK_RET_SUCCESS;
}

void SkBspInitSensor() {
    // 等待I2C总线稳定，避免与音频设备初始化冲突
    vTaskDelay(pdMS_TO_TICKS(100));

#if CONFIG_SK_GYRO_DEV_ENABLE
    ESP_LOGI(TAG, "Initializing gyroscope...");
    if (SkGyroInit(g_skBoard.i2cBusSensor) != SK_RET_SUCCESS) {
        ESP_LOGE(TAG, "Failed to initialize gyroscope");
    } else {
        ESP_LOGI(TAG, "Gyroscope initialized successfully");
    }
#endif
#if CONFIG_SK_THS_DEV_ENABLE
    ESP_LOGI(TAG, "Initializing temperature/humidity sensor...");
    if (SkTHSInit(g_skBoard.i2cBusSensor) != SK_RET_SUCCESS) {
        ESP_LOGE(TAG, "Failed to initialize THS sensor");
    } else {
        ESP_LOGI(TAG, "THS sensor initialized successfully");
    }
#endif
    return;
}

sk_err_t SkBspReadAudio(int16_t *buffer, int bufferLen) {
#ifdef CONFIG_NONCODEC_DEV
    return AudioDevReadNoncodec(buffer, bufferLen);    
#else
    return AudioDevRead(buffer, bufferLen);
#endif
}

sk_err_t SkBspStartSpk() {
#ifdef CONFIG_NONCODEC_DEV
    AudioDevEnableSpkNoncodec(true);
#else
    AudioDevEnableSpk(true);
#endif
    return SK_RET_SUCCESS;
}

sk_err_t SkBspStopSpk() {
#ifdef CONFIG_NONCODEC_DEV
    AudioDevEnableSpkNoncodec(false);
#else
    AudioDevEnableSpk(false);
#endif
    return SK_RET_SUCCESS;
}

sk_err_t SkBspPlayAudio(const int16_t* data, size_t length, uint32_t msToWait) {
#ifdef CONFIG_NONCODEC_DEV
    return AudioDevWriteNoncodec(data, length, msToWait);
#else
    return AudioDevWrite(data, length);
#endif
}

sk_err_t SkBspPlayPreload(const int16_t* data, size_t length, size_t *wBytes) {
#ifdef CONFIG_NONCODEC_DEV
    return AudioDevPreloadNoncodec(data, length, wBytes);
#else
    *wBytes = 0;
    return SK_RET_SUCCESS;
#endif
}

sk_err_t SkBspSetPlayVol(int vol) {
#ifdef CONFIG_NONCODEC_DEV
    AudioDevSetSpkVolNoncodec(vol);
#else
    AudioDevSetSpkVol(vol);
#endif
    return SK_RET_SUCCESS;
}

int32_t SkBspGetPlayVol() {
#ifdef CONFIG_NONCODEC_DEV
    return AudioDevGetSpkVolNoncodec();
#else
    return AudioDevGetSpkVol();
#endif
}

sk_err_t SkBspSetMicVol(int vol) {
#ifdef CONFIG_NONCODEC_DEV
    AudioDevSetMicVolNoncodec(vol);
#else
    AudioDevSetMicVol(vol);
#endif
    return SK_RET_SUCCESS;
}

int32_t SkBspGetMicVol() {
#ifdef CONFIG_NONCODEC_DEV
    return AudioDevGetMicVolNoncodec();
#else
    return AudioDevGetMicVol();
#endif
}

int32_t SkBspI2cInit(SkBoard *board) {
    i2c_master_bus_config_t i2cBusCfg = {
        .i2c_port = (i2c_port_t)1,
        .sda_io_num = AUDIO_CODEC_I2C_SDA_PIN,
        .scl_io_num = AUDIO_CODEC_I2C_SCL_PIN,
        .clk_source = I2C_CLK_SRC_DEFAULT,
        .glitch_ignore_cnt = 7,
        .intr_priority = 0,
        .trans_queue_depth = 0,
        .flags = {
            .enable_internal_pullup = 1,
        },
    };
    ESP_ERROR_CHECK(i2c_new_master_bus(&i2cBusCfg, &board->i2cBus));
#if CONFIG_I2C_MASTER_NUM == 1
    board->i2cBusSensor = board->i2cBus;
#else
    i2cBusCfg.i2c_port = (i2c_port_t)0;
    i2cBusCfg.sda_io_num = SENSOR_I2C_SDA_PIN;
    i2cBusCfg.scl_io_num = SENSOR_I2C_SCL_PIN;
    ESP_ERROR_CHECK(i2c_new_master_bus(&i2cBusCfg, &board->i2cBusSensor));
    ESP_LOGI(TAG, "I2C Sensor Bus Initialized");
#endif
    return SK_RET_SUCCESS;
}

void SkBspShowStat() {
    return;
}

void SkBspEnterSleep(void) {
    gpio_intr_disable(GPIO_KEY_INPUT);
#if CONFIG_SK_PM_SLEEP_MODE == CONFIG_SK_PM_SLEEP_MODE_LIGHT
    gpio_wakeup_enable(GPIO_KEY_INPUT, GPIO_INTR_LOW_LEVEL);
    esp_sleep_enable_gpio_wakeup();
    esp_light_sleep_start();
#elif CONFIG_SK_PM_SLEEP_MODE == CONFIG_SK_PM_SLEEP_MODE_DEEP
    ESP_ERROR_CHECK(esp_sleep_enable_ext0_wakeup(GPIO_KEY_INPUT, 0));
    // Configure pullup/downs via RTCIO to tie wakeup pins to inactive level during deepsleep.
    // EXT0 resides in the same power domain (RTC_PERIPH) as the RTC IO pullup/downs.
    // No need to keep that power domain explicitly, unlike EXT1.
    ESP_ERROR_CHECK(rtc_gpio_pullup_en(GPIO_KEY_INPUT));
    ESP_ERROR_CHECK(rtc_gpio_pulldown_dis(GPIO_KEY_INPUT));
    esp_deep_sleep_start();
#endif
    return;
}

void SkBoardSetPm(bool flag) {
    // 不做任何处理, WiFi重启和音频重启时。分配内存，由于内存碎片导致WiFi无法启动。
    if (flag) {
        // GPIO隔离
        // I2S和I2C暂停
        //SkBspEnableSpk(false);
        AudioDevEnableMic(false);
    } else {
        //SkBspEnableSpk(true);
        AudioDevEnableMic(true);
    }
}

void SkBoardModemSetPm(bool flag) {
    if (flag) {
        esp_wifi_set_ps(WIFI_PS_MAX_MODEM);
        esp_pm_config_t pm_config = {
            .max_freq_mhz = 240,
            .min_freq_mhz = 40,
            .light_sleep_enable = false,
        };
        esp_pm_configure(&pm_config);
    } else {
        esp_wifi_set_ps(WIFI_PS_NONE);
        esp_pm_config_t pm_config = {
            .max_freq_mhz = 240,
            .min_freq_mhz = 240,
            .light_sleep_enable = false,
        };
        esp_pm_configure(&pm_config);
    }
}

int32_t SkBspPmTimeWakeup(int32_t wakeupSec) {
    esp_err_t ret;

    ret = esp_sleep_enable_timer_wakeup(wakeupSec * 1000000);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "esp_sleep_enable_timer_wakeup failed");
        return SK_RET_FAIL;
    }
    return SK_RET_SUCCESS;
}

int32_t SkBspGetFuncKey() {
#if (CONFIG_FUNC_KEY_MODE == CONFIG_SK_NORMAL_KEY)
    return SkBspGetFuncKeyState();
#else
    return SkBspGetAdcKey();
#endif
}

int32_t SkBspGetChipID(uint8_t *chipID, size_t bufSize) {
    // CHIP ID长度为8Byte
    if (bufSize < 8) {
        return SK_RET_FAIL;
    }
    esp_efuse_mac_get_default(chipID);
    return SK_RET_SUCCESS;
}
