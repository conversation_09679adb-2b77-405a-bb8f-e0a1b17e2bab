# 温湿度传感器数据集成说明

## 修改概述

已成功将温湿度传感器数据集成到现有的陀螺仪数据处理流程中，实现了温湿度数据与陀螺仪数据的同步打印和TCP发送。

## 主要修改内容

### 1. 修改文件
- `main/app/my_app.c` - 在 `process_sensor_data()` 函数中添加温湿度数据处理

### 2. 具体修改

#### 2.1 添加温湿度数据读取
```c
// 读取温湿度传感器数据
SkTHSData_t thsData;
bool thsReadSuccess = false;
if (SkTHSReadData(&thsData) == SK_RET_SUCCESS) {
    thsReadSuccess = true;
}
```

#### 2.2 修改数据输出格式
- **成功读取时的格式**：
  ```
  FT_2:SkBsp: BAT Raw Data: [电池数据]
  MyApp: Sample #[序号]: Accel[x,y,z]g Total=[总加速度]g Gyro[x,y,z]°/s Temp=[温度]°C Steps=[步数] THS_Temp=[温湿度传感器温度]°C THS_Humi=[湿度]%[错误信息]
  ```

- **读取失败时的格式**：
  ```
  FT_2:SkBsp: BAT Raw Data: [电池数据]
  MyApp: Sample #[序号]: Accel[x,y,z]g Total=[总加速度]g Gyro[x,y,z]°/s Temp=[温度]°C Steps=[步数] THS_Temp=N/A THS_Humi=N/A[错误信息]
  ```

#### 2.3 错误处理机制
- 如果温湿度传感器读取成功，显示实际的温度和湿度值
- 如果温湿度传感器读取失败，显示 "N/A" 标识
- 不影响陀螺仪数据的正常处理和发送

## 数据格式说明

### 温湿度数据类型
- `thsData.temp`: `int32_t` 类型，单位：摄氏度
- `thsData.humi`: `int32_t` 类型，单位：百分比

### 输出示例
```
FT_2:SkBsp: BAT Raw Data: 2048
MyApp: Sample #1: Accel[0.123,0.456,0.789]g Total=0.234g Gyro[1.23,4.56,7.89]°/s Temp=25.5°C Steps=100 THS_Temp=24.6°C THS_Humi=65.4%
```

## 功能特性

1. **同步数据采集**：温湿度数据与陀螺仪数据在同一个采样周期内读取
2. **统一输出格式**：温湿度数据集成到现有的数据输出格式中
3. **错误容错**：温湿度传感器故障不影响陀螺仪数据的正常工作
4. **TCP传输**：温湿度数据随陀螺仪数据一起通过TCP发送到服务器
5. **控制台显示**：温湿度数据同时在控制台打印显示

## 使用方法

1. 按下 FUNC_KEY1 (GPIO0) 开始数据采集
2. 系统将每10秒采集一次传感器数据，包括：
   - 陀螺仪数据（加速度、角速度、温度、步数）
   - 温湿度传感器数据（温度、湿度）
   - 电池电量数据
3. 数据同时显示在控制台和发送到TCP服务器
4. 再次按下 FUNC_KEY1 停止数据采集

## 编译状态

✅ 编译成功，无错误和警告
✅ 代码格式化符合项目规范
✅ 使用正确的 PRId32 宏处理 int32_t 类型格式化

## 温湿度数据精度优化

为了提供更精确的温湿度显示，已优化数据存储和显示方式：

### 数据存储优化
- **存储方式**: 将温湿度数据乘以10存储在 `int32_t` 中
- **温度**: 25.6°C 存储为 256
- **湿度**: 65.4% 存储为 654

### 显示格式优化
- **温度**: 显示1位小数，如 `24.6°C`
- **湿度**: 显示1位小数，如 `65.4%`
- **格式化**: 使用 `%.1f` 格式，通过 `/10.0f` 转换显示

### 修改的文件
1. **`main/board/sk_ths.c`**: 修改数据存储逻辑
2. **`main/app/my_app.c`**: 修改显示逻辑
3. **`main/app/sk_sensors.c`**: 修改调试日志

## 板子配置修改

已将板子配置从 `CONFIG_ESP32_S3_BOX3` 修改为 `CONFIG_DEMO_BOARD_V_0_1`（对应 `CONFIG_FT_BOARD_V_0_1`）：

- **修改文件**: `sdkconfig`
- **配置变更**: `CONFIG_BOARD_TYPE=3` (FT_BOARD_V_0_1)
- **温湿度传感器**: 在 FT_BOARD_V_0_1 配置中已启用 (`CONFIG_SK_THS_DEV_ENABLE=1`)

## GPIO引脚配置修复

修复了FT_BOARD_V_0_1配置中的GPIO引脚定义，确保与ESP32-S3兼容：

### 修改的文件
- **`main/board/sk_aec_board.h`**: 修复GPIO引脚定义

### GPIO引脚变更
```c
// 修改前（不兼容ESP32-S3）
#define GPIO_FUNC_KEY1    (GPIO_NUM_22)  // ESP32-S3没有GPIO22
#define GPIO_FUNC_KEY2    (GPIO_NUM_41)

// 修改后（兼容ESP32-S3）
#define GPIO_FUNC_KEY1    (GPIO_NUM_0)   // 使用ESP32-S3可用的GPIO0
#define GPIO_FUNC_KEY2    (GPIO_NUM_1)   // 使用ESP32-S3可用的GPIO1
```

### ESP32-S3 GPIO可用范围
- **可用GPIO**: 0-21, 26, 33-48
- **不可用**: 22-25, 27-32 (这些GPIO在ESP32-S3中不存在或有特殊用途)

## 注意事项

1. 确保温湿度传感器硬件连接正常
2. 项目配置中已启用温湿度传感器功能 (`CONFIG_SK_THS_ENABLE=1`)
3. 温湿度传感器使用 AHT30，I2C地址为 0x38
4. 如果温湿度传感器初始化失败，不会影响其他功能的正常运行
5. **温湿度数据现在显示1位小数**，提供更精确的读数
6. **GPIO引脚已修复**，确保与ESP32-S3硬件兼容
7. **按键功能**: FUNC_KEY1 (GPIO0) 控制陀螺仪数据采集，FUNC_KEY2 (GPIO1) 触发OTA升级
