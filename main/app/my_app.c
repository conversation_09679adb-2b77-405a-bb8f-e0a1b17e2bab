/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: my_app.c
 * @description: 自定义应用程序 - 通过GPIO9按键控制陀螺仪数据读取
 * @author: User
 * @date: 2025-07-12
 */
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <math.h>
#include <inttypes.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <errno.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "freertos/timers.h"
#include "driver/gpio.h"
#include "esp_log.h"
#include "sk_board.h"
#include "sk_common.h"
#include "sk_os.h"
#include "sk_sm.h"

#define TAG "MyApp"

// 使用FT开发板的功能按键1 (GPIO42) - 大数据模式控制
#define BIG_DATA_KEY        1  // 对应GPIO_FUNC_KEY1 (GPIO42)
// 使用FT开发板的功能按键2 (GPIO41) - 小数据发送
#define SMALL_DATA_KEY      2  // 对应GPIO_FUNC_KEY2 (GPIO41)

// 事件位定义
#define BIG_DATA_START_BIT      BIT0
#define BIG_DATA_STOP_BIT       BIT1
#define SMALL_DATA_SEND_BIT     BIT2
#define TASK_EXIT_BIT           BIT3

// 数据数组配置
#define BIG_DATA_ARRAY_SIZE     50      // 大数据数组大小
#define SMALL_DATA_ARRAY_SIZE   5       // 小数据数组大小
#define BIG_DATA_SEND_INTERVAL_MS   500 // 大数据发送间隔(ms)
#define DATA_COLLECT_INTERVAL_MS    1000 // 数据收集间隔(ms)

// 陀螺仪测试配置
#define GYRO_READ_INTERVAL_MS   10000     // 读取间隔(ms)
#define GYRO_RESET_INTERVAL_MS  120000    // 陀螺仪复位间隔(ms) - 2分钟

// TCP服务器配置
#define TCP_SERVER_IP           "************"  // 服务器IP地址
#define TCP_SERVER_PORT         8080            // 服务器端口
#define TCP_RECONNECT_DELAY_MS  5000           // 重连延时(ms)
#define TCP_SEND_TIMEOUT_MS     1000           // 发送超时(ms)

// 数据样本结构
typedef struct {
    SkSensorData_t sensorData;
    SkTHSData_t thsData;
    int32_t batteryRaw;
    uint32_t timestamp;
    bool thsValid;
} DataSample_t;

// 大数据数组结构
typedef struct {
    DataSample_t samples[BIG_DATA_ARRAY_SIZE];
    uint32_t writeIndex;
    uint32_t readIndex;
    uint32_t count;
    bool isFull;
    bool isCollecting;
    bool isSending;
} BigDataArray_t;

// 小数据数组结构
typedef struct {
    DataSample_t samples[SMALL_DATA_ARRAY_SIZE];
    uint32_t count;
} SmallDataArray_t;

typedef struct {
    EventGroupHandle_t eventGroup;
    TaskHandle_t dataTaskHandle;
    TaskHandle_t keyMonitorTaskHandle;
    TaskHandle_t tcpTaskHandle;
    TaskHandle_t bigDataSendTaskHandle;
    bool isRunning;
    uint32_t readCount;
    uint32_t successCount;
    uint32_t failCount;
    int32_t lastKeyState;
    uint32_t startTime;

    // TCP连接相关
    int tcpSocket;
    bool tcpConnected;
    uint32_t tcpSendCount;
    uint32_t tcpSendFailCount;

    // 数据数组
    BigDataArray_t bigDataArray;
    SmallDataArray_t smallDataArray;

    // 模式状态
    bool bigDataMode;
    bool smallDataMode;
} MyAppCtrl_t;

static MyAppCtrl_t g_myAppCtrl = {0};

/**
 * @brief 陀螺仪复位定时器回调函数
 */
static void gyro_reset_timer_callback(TimerHandle_t xTimer)
{
    MyAppCtrl_t* ctrl = (MyAppCtrl_t*)pvTimerGetTimerID(xTimer);
    BaseType_t xHigherPriorityTaskWoken = pdFALSE;

    // 在定时器回调中只发送事件通知，绝对最小化操作以避免堆栈溢出
    if (ctrl != NULL && ctrl->eventGroup != NULL) {
        xEventGroupSetBitsFromISR(ctrl->eventGroup, GYRO_RESET_BIT, &xHigherPriorityTaskWoken);
        portYIELD_FROM_ISR(xHigherPriorityTaskWoken);
    }
}

/**
 * @brief TCP连接函数
 */
static int tcp_connect(void)
{
    int sock;
    struct sockaddr_in server_addr;
    struct timeval timeout;

    // 创建socket
    sock = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (sock < 0) {
        ESP_LOGE(TAG, "Failed to create socket: errno %d", errno);
        return -1;
    }

    // 设置发送超时
    timeout.tv_sec = TCP_SEND_TIMEOUT_MS / 1000;
    timeout.tv_usec = (TCP_SEND_TIMEOUT_MS % 1000) * 1000;
    if (setsockopt(sock, SOL_SOCKET, SO_SNDTIMEO, &timeout, sizeof(timeout)) < 0) {
        ESP_LOGW(TAG, "Failed to set send timeout");
    }

    // 设置接收超时
    if (setsockopt(sock, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout)) < 0) {
        ESP_LOGW(TAG, "Failed to set recv timeout");
    }

    // 配置服务器地址
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(TCP_SERVER_PORT);
    if (inet_pton(AF_INET, TCP_SERVER_IP, &server_addr.sin_addr) <= 0) {
        ESP_LOGE(TAG, "Invalid server IP address: %s", TCP_SERVER_IP);
        close(sock);
        return -1;
    }

    // 连接服务器
    if (connect(sock, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
        ESP_LOGE(TAG, "Failed to connect to %s:%d, errno %d", TCP_SERVER_IP, TCP_SERVER_PORT, errno);
        close(sock);
        return -1;
    }

    ESP_LOGI(TAG, "Successfully connected to TCP server %s:%d", TCP_SERVER_IP, TCP_SERVER_PORT);
    return sock;
}

/**
 * @brief TCP断开连接函数
 */
static void tcp_disconnect(int sock)
{
    if (sock >= 0) {
        close(sock);
        ESP_LOGI(TAG, "TCP connection closed");
    }
}

/**
 * @brief 发送数据到TCP服务器
 */
static int tcp_send_data(int sock, const char* data, size_t len)
{
    if (sock < 0 || data == NULL || len == 0) {
        return -1;
    }

    int sent = send(sock, data, len, 0);
    if (sent < 0) {
        ESP_LOGE(TAG, "Failed to send data: errno %d", errno);
        return -1;
    } else if (sent != len) {
        ESP_LOGW(TAG, "Partial send: %d/%d bytes", sent, len);
        return sent;
    }

    return sent;
}

/**
 * @brief 按键监控任务
 */
static void key_monitor_task(void* pvParameters)
{
    MyAppCtrl_t* ctrl = (MyAppCtrl_t*)pvParameters;
    int32_t currentKeyState;
    int32_t lastKeyState = 0;  // 使用局部变量减少结构体访问

    ESP_LOGI(TAG, "Key monitor task started with 4KB stack");

    while (1) {
        // 使用工程内置的按键读取函数
        currentKeyState = SkBspGetFuncKey();

        // 检测按键按下事件（从0变为非0）
        if (lastKeyState == 0 && currentKeyState != 0) {
            ESP_LOGI(TAG, "Key pressed: %d", currentKeyState);

            switch (currentKeyState) {
                case BIG_DATA_KEY:
                    // 大数据模式控制按键
                    if (ctrl->bigDataMode) {
                        // 当前正在大数据模式，发送停止信号
                        xEventGroupSetBits(ctrl->eventGroup, BIG_DATA_STOP_BIT);
                        ESP_LOGI(TAG, "Stopping big data mode...");
                    } else {
                        // 当前未运行，发送启动信号
                        xEventGroupSetBits(ctrl->eventGroup, BIG_DATA_START_BIT);
                        ESP_LOGI(TAG, "Starting big data mode...");
                    }
                    break;

                case SMALL_DATA_KEY:
                    // 小数据发送按键
                    ESP_LOGI(TAG, "Small data send triggered by key press!");
                    xEventGroupSetBits(ctrl->eventGroup, SMALL_DATA_SEND_BIT);
                    break;

                default:
                    ESP_LOGW(TAG, "Unknown key pressed: %d", currentKeyState);
                    break;
            }
        }

        lastKeyState = currentKeyState;
        ctrl->lastKeyState = currentKeyState;  // 同步到结构体
        vTaskDelay(pdMS_TO_TICKS(100)); // 100ms检测间隔
    }
}

/**
 * @brief 收集数据样本
 */
static bool collect_data_sample(DataSample_t* sample)
{
    // 读取陀螺仪数据
    if (SkGyroReadData(&sample->sensorData) != SK_RET_SUCCESS) {
        return false;
    }

    // 读取温湿度数据
    sample->thsValid = (SkTHSReadData(&sample->thsData) == SK_RET_SUCCESS);

    // 读取电池数据
    sample->batteryRaw = SkBspGetBattery();

    // 记录时间戳
    sample->timestamp = xTaskGetTickCount();

    return true;
}

/**
 * @brief 添加数据到大数据数组
 */
static void add_to_big_data_array(BigDataArray_t* array, const DataSample_t* sample)
{
    array->samples[array->writeIndex] = *sample;
    array->writeIndex = (array->writeIndex + 1) % BIG_DATA_ARRAY_SIZE;

    if (array->count < BIG_DATA_ARRAY_SIZE) {
        array->count++;
    } else {
        array->isFull = true;
        // 如果数组满了，移动读取索引
        array->readIndex = (array->readIndex + 1) % BIG_DATA_ARRAY_SIZE;
    }
}

/**
 * @brief 添加数据到小数据数组
 */
static void add_to_small_data_array(SmallDataArray_t* array, const DataSample_t* sample)
{
    if (array->count < SMALL_DATA_ARRAY_SIZE) {
        array->samples[array->count] = *sample;
        array->count++;
    } else {
        // 数组满了，移动数据，添加新数据到末尾
        for (int i = 0; i < SMALL_DATA_ARRAY_SIZE - 1; i++) {
            array->samples[i] = array->samples[i + 1];
        }
        array->samples[SMALL_DATA_ARRAY_SIZE - 1] = *sample;
    }
}

/**
 * @brief 格式化数据样本为字符串
 */
static int format_data_sample(const DataSample_t* sample, char* buffer, size_t bufferSize,
                             uint32_t index, const char* prefix)
{
    // 转换系数
    float accelScale = 2.0f / 32768.0f;    // ±2g量程
    float gyroScale = 250.0f / 32768.0f;   // ±250dps量程
    float tempScale = 1.0f / 128.0f;       // 温度转换系数

    // 转换为物理量
    float accelX = (float)sample->sensorData.accel[0] * accelScale;
    float accelY = (float)sample->sensorData.accel[1] * accelScale;
    float accelZ = (float)sample->sensorData.accel[2] * accelScale;
    float accelTotal = sqrt(accelX * accelX + accelY * accelY + accelZ * accelZ) - 1.0f;

    float gyroX = (float)sample->sensorData.gyro[0] * gyroScale;
    float gyroY = (float)sample->sensorData.gyro[1] * gyroScale;
    float gyroZ = (float)sample->sensorData.gyro[2] * gyroScale;

    float temperature = (float)sample->sensorData.temp * tempScale + 25.0f;

    // 格式化字符串
    int len;
    if (sample->thsValid) {
        len = snprintf(buffer, bufferSize,
            "%s:SkBsp: BAT Raw Data: %" PRId32 "\n%s: Sample #%" PRIu32 ": Accel[%.3f,%.3f,%.3f]g Total=%.3fg Gyro[%.2f,%.2f,%.2f]°/s Temp=%.1f°C Steps=%d THS_Temp=%.1f°C THS_Humi=%.1f%%\n",
            prefix, sample->batteryRaw, prefix, index, accelX, accelY, accelZ, accelTotal,
            gyroX, gyroY, gyroZ, temperature, sample->sensorData.step,
            sample->thsData.temp / 10.0f, sample->thsData.humi / 10.0f);
    } else {
        len = snprintf(buffer, bufferSize,
            "%s:SkBsp: BAT Raw Data: %" PRId32 "\n%s: Sample #%" PRIu32 ": Accel[%.3f,%.3f,%.3f]g Total=%.3fg Gyro[%.2f,%.2f,%.2f]°/s Temp=%.1f°C Steps=%d THS_Temp=N/A THS_Humi=N/A\n",
            prefix, sample->batteryRaw, prefix, index, accelX, accelY, accelZ, accelTotal,
            gyroX, gyroY, gyroZ, temperature, sample->sensorData.step);
    }

    return len;
}

/**
 * @brief 数据处理函数 - 将原始数据转换为物理量并发送到TCP服务器
 */
static void process_sensor_data(SkSensorData_t* data, uint32_t index)
{
    MyAppCtrl_t* ctrl = &g_myAppCtrl;

    // 转换系数
    float accelScale = 2.0f / 32768.0f;    // ±2g量程
    float gyroScale = 250.0f / 32768.0f;   // ±250dps量程
    float tempScale = 1.0f / 128.0f;       // 温度转换系数

    // 转换为物理量
    float accelX = (float)data->accel[0] * accelScale;
    float accelY = (float)data->accel[1] * accelScale;
    float accelZ = (float)data->accel[2] * accelScale;
    float accelTotal = sqrt(accelX * accelX + accelY * accelY + accelZ * accelZ) - 1.0f;

    float gyroX = (float)data->gyro[0] * gyroScale;
    float gyroY = (float)data->gyro[1] * gyroScale;
    float gyroZ = (float)data->gyro[2] * gyroScale;

    float temperature = (float)data->temp * tempScale + 25.0f;

    // 读取电池ADC原始数据
    int32_t batteryRaw = SkBspGetBattery();

    // 读取温湿度传感器数据
    SkTHSData_t thsData;
    bool thsReadSuccess = false;
    if (SkTHSReadData(&thsData) == SK_RET_SUCCESS) {
        thsReadSuccess = true;
    }

    // 检查是否有I2C读取失败，如果有则在日志中标注
    const char* failInfo = "";
    char failBuffer[128] = {0};
    if (data->readFailFlags != 0) {
        snprintf(failBuffer, sizeof(failBuffer), " [I2C_READ_FAILED: %s%s%s%s]",
                (data->readFailFlags & 0x01) ? "ACCEL " : "",
                (data->readFailFlags & 0x02) ? "GYRO " : "",
                (data->readFailFlags & 0x04) ? "FF " : "",
                (data->readFailFlags & 0x08) ? "PEDOMETER " : "");
        failInfo = failBuffer;
    }

    // 构造发送数据字符串
    char sendBuffer[512];
    int len;
    if (thsReadSuccess) {
        len = snprintf(sendBuffer, sizeof(sendBuffer),
            "FT_2:SkBsp: BAT Raw Data: %" PRId32 "\nMyApp: Sample #%" PRIu32 ": Accel[%.3f,%.3f,%.3f]g Total=%.3fg Gyro[%.2f,%.2f,%.2f]°/s Temp=%.1f°C Steps=%d THS_Temp=%.1f°C THS_Humi=%.1f%%%s\n",
            batteryRaw, index, accelX, accelY, accelZ, accelTotal, gyroX, gyroY, gyroZ, temperature, data->step, thsData.temp / 10.0f, thsData.humi / 10.0f, failInfo);
    } else {
        len = snprintf(sendBuffer, sizeof(sendBuffer),
            "FT_2:SkBsp: BAT Raw Data: %" PRId32 "\nMyApp: Sample #%" PRIu32 ": Accel[%.3f,%.3f,%.3f]g Total=%.3fg Gyro[%.2f,%.2f,%.2f]°/s Temp=%.1f°C Steps=%d THS_Temp=N/A THS_Humi=N/A%s\n",
            batteryRaw, index, accelX, accelY, accelZ, accelTotal, gyroX, gyroY, gyroZ, temperature, data->step, failInfo);
    }

    // 打印到控制台
    if (thsReadSuccess) {
        ESP_LOGI(TAG, "FT_2:Sample #%" PRIu32 ": Accel[%.3f,%.3f,%.3f]g Total=%.3fg Gyro[%.2f,%.2f,%.2f]°/s Temp=%.1f°C Steps=%d THS_Temp=%.1f°C THS_Humi=%.1f%%%s",
                 index, accelX, accelY, accelZ, accelTotal, gyroX, gyroY, gyroZ, temperature, data->step, thsData.temp / 10.0f, thsData.humi / 10.0f, failInfo);
    } else {
        ESP_LOGI(TAG, "FT_2:Sample #%" PRIu32 ": Accel[%.3f,%.3f,%.3f]g Total=%.3fg Gyro[%.2f,%.2f,%.2f]°/s Temp=%.1f°C Steps=%d THS_Temp=N/A THS_Humi=N/A%s",
                 index, accelX, accelY, accelZ, accelTotal, gyroX, gyroY, gyroZ, temperature, data->step, failInfo);
    }

    // 发送到TCP服务器
    if (ctrl->tcpConnected && ctrl->tcpSocket >= 0) {
        if (tcp_send_data(ctrl->tcpSocket, sendBuffer, len) > 0) {
            ctrl->tcpSendCount++;
        } else {
            ctrl->tcpSendFailCount++;
            ESP_LOGW(TAG, "Failed to send data to TCP server");
        }
    }
}

/**
 * @brief TCP连接管理任务
 */
static void tcp_connection_task(void* pvParameters)
{
    MyAppCtrl_t* ctrl = (MyAppCtrl_t*)pvParameters;

    ESP_LOGI(TAG, "TCP connection task started");

    while (1) {
        // 检查是否需要退出
        EventBits_t bits = xEventGroupWaitBits(ctrl->eventGroup, GYRO_TASK_EXIT_BIT,
                                               pdFALSE, pdFALSE, 0);
        if (bits & GYRO_TASK_EXIT_BIT) {
            ESP_LOGI(TAG, "TCP task exit requested");
            break;
        }

        if (!ctrl->tcpConnected) {
            ESP_LOGI(TAG, "Attempting to connect to TCP server %s:%d", TCP_SERVER_IP, TCP_SERVER_PORT);

            ctrl->tcpSocket = tcp_connect();
            if (ctrl->tcpSocket >= 0) {
                ctrl->tcpConnected = true;
                ESP_LOGI(TAG, "TCP connection established");
            } else {
                ESP_LOGW(TAG, "TCP connection failed, retrying in %d ms", TCP_RECONNECT_DELAY_MS);
                vTaskDelay(pdMS_TO_TICKS(TCP_RECONNECT_DELAY_MS));
                continue;
            }
        }

        // // 连接已建立，检查连接状态
        // if (ctrl->tcpConnected) {
        //     // 发送心跳包或检查连接状态
        //     char heartbeat[] = "";
        //     if (tcp_send_data(ctrl->tcpSocket, heartbeat, strlen(heartbeat)) < 0) {
        //         ESP_LOGW(TAG, "TCP connection lost, attempting to reconnect");
        //         tcp_disconnect(ctrl->tcpSocket);
        //         ctrl->tcpSocket = -1;
        //         ctrl->tcpConnected = false;
        //     }
        // }

        // 每5秒检查一次连接状态
        vTaskDelay(pdMS_TO_TICKS(5000));
    }

    // 清理TCP连接
    if (ctrl->tcpConnected) {
        tcp_disconnect(ctrl->tcpSocket);
        ctrl->tcpSocket = -1;
        ctrl->tcpConnected = false;
    }

    ESP_LOGI(TAG, "TCP connection task ended");
    vTaskDelete(NULL);
}

/**
 * @brief 大数据发送任务
 */
static void big_data_send_task(void* pvParameters)
{
    MyAppCtrl_t* ctrl = (MyAppCtrl_t*)pvParameters;
    char sendBuffer[512];
    uint32_t sendIndex = 0;

    ESP_LOGI(TAG, "Big data send task started");

    while (1) {
        if (!ctrl->bigDataArray.isSending) {
            vTaskDelay(pdMS_TO_TICKS(100));
            continue;
        }

        BigDataArray_t* array = &ctrl->bigDataArray;

        // 检查是否有数据要发送
        if (array->count == 0) {
            vTaskDelay(pdMS_TO_TICKS(100));
            continue;
        }

        // 发送当前读取位置的数据
        const DataSample_t* sample = &array->samples[array->readIndex];
        int len = format_data_sample(sample, sendBuffer, sizeof(sendBuffer),
                                   sendIndex++, "BigData");

        // 打印到控制台
        ESP_LOGI(TAG, "BigData[%d/%d]: Sample #%d",
                 array->readIndex + 1, array->count, sendIndex - 1);

        // 发送到TCP服务器
        if (ctrl->tcpConnected && ctrl->tcpSocket >= 0) {
            if (tcp_send_data(ctrl->tcpSocket, sendBuffer, len) > 0) {
                ctrl->tcpSendCount++;
            } else {
                ctrl->tcpSendFailCount++;
                ESP_LOGW(TAG, "Failed to send big data to TCP server");
            }
        }

        // 移动到下一个数据
        array->readIndex = (array->readIndex + 1) % BIG_DATA_ARRAY_SIZE;
        if (array->readIndex == array->writeIndex) {
            // 已经发送完所有数据，重新开始
            sendIndex = 0;
            ESP_LOGI(TAG, "Big data array sent completely, restarting...");
        }

        // 发送间隔
        vTaskDelay(pdMS_TO_TICKS(BIG_DATA_SEND_INTERVAL_MS));
    }

    ESP_LOGI(TAG, "Big data send task ended");
    vTaskDelete(NULL);
}

/**
 * @brief 发送小数据数组
 */
static void send_small_data_array(MyAppCtrl_t* ctrl)
{
    char sendBuffer[512];
    SmallDataArray_t* array = &ctrl->smallDataArray;

    ESP_LOGI(TAG, "Sending small data array (%d samples)", array->count);

    for (uint32_t i = 0; i < array->count; i++) {
        const DataSample_t* sample = &array->samples[i];
        int len = format_data_sample(sample, sendBuffer, sizeof(sendBuffer),
                                   i, "SmallData");

        // 打印到控制台
        ESP_LOGI(TAG, "SmallData[%d/%d]: Sample #%d",
                 i + 1, array->count, i);

        // 发送到TCP服务器
        if (ctrl->tcpConnected && ctrl->tcpSocket >= 0) {
            if (tcp_send_data(ctrl->tcpSocket, sendBuffer, len) > 0) {
                ctrl->tcpSendCount++;
            } else {
                ctrl->tcpSendFailCount++;
                ESP_LOGW(TAG, "Failed to send small data to TCP server");
            }
        }

        // 发送间隔
        vTaskDelay(pdMS_TO_TICKS(100));
    }

    ESP_LOGI(TAG, "Small data array sent completely");
}

/**
 * @brief 数据处理任务
 */
static void data_process_task(void* pvParameters)
{
    MyAppCtrl_t* ctrl = (MyAppCtrl_t*)pvParameters;
    DataSample_t sample;
    EventBits_t bits;

    ESP_LOGI(TAG, "Data process task started");

    while (1) {
        // 等待事件信号
        bits = xEventGroupWaitBits(ctrl->eventGroup,
                                   BIG_DATA_START_BIT | BIG_DATA_STOP_BIT |
                                   SMALL_DATA_SEND_BIT | TASK_EXIT_BIT,
                                   pdTRUE, pdFALSE, portMAX_DELAY);

        if (bits & TASK_EXIT_BIT) {
            ESP_LOGI(TAG, "Data process task exit requested");
            break;
        }

        if (bits & BIG_DATA_START_BIT) {
            ESP_LOGI(TAG, "=== Starting Big Data Mode ===");
            ESP_LOGI(TAG, "Press FUNC_KEY1 again to stop big data mode");

            // 初始化大数据模式
            ctrl->bigDataMode = true;
            ctrl->bigDataArray.writeIndex = 0;
            ctrl->bigDataArray.readIndex = 0;
            ctrl->bigDataArray.count = 0;
            ctrl->bigDataArray.isFull = false;
            ctrl->bigDataArray.isCollecting = true;
            ctrl->bigDataArray.isSending = false;

            ctrl->readCount = 0;
            ctrl->successCount = 0;
            ctrl->failCount = 0;

            // 创建并启动TCP连接管理任务
            if (ctrl->tcpTaskHandle == NULL) {
                BaseType_t ret = xTaskCreate(tcp_connection_task, "TcpConnTask", 4096,
                                           ctrl, 3, &ctrl->tcpTaskHandle);
                if (ret == pdPASS) {
                    ESP_LOGI(TAG, "TCP connection task created and started");
                } else {
                    ESP_LOGW(TAG, "Failed to create TCP connection task");
                }
            }

            // 创建并启动大数据发送任务
            if (ctrl->bigDataSendTaskHandle == NULL) {
                BaseType_t ret = xTaskCreate(big_data_send_task, "BigDataSendTask", 4096,
                                           ctrl, 4, &ctrl->bigDataSendTaskHandle);
                if (ret == pdPASS) {
                    ESP_LOGI(TAG, "Big data send task created and started");
                } else {
                    ESP_LOGW(TAG, "Failed to create big data send task");
                }
            }

            // 持续读取数据，直到收到停止信号
            while (ctrl->isRunning) {
                ctrl->readCount++;

                // 调用工程内置的陀螺仪读取函数
                if (SkGyroReadData(&sensorData) == SK_RET_SUCCESS) {
                    ctrl->successCount++;
                    process_sensor_data(&sensorData, ctrl->readCount);
                } else {
                    ctrl->failCount++;
                    ESP_LOGW(TAG, "Failed to read gyro data at sample #%d", ctrl->readCount);
                }

                // 检查是否有停止信号或复位信号（非阻塞检查）
                bits = xEventGroupWaitBits(ctrl->eventGroup, GYRO_TASK_STOP_BIT | GYRO_RESET_BIT,
                                           pdTRUE, pdFALSE, 0);
                if (bits & GYRO_TASK_STOP_BIT) {
                    ESP_LOGI(TAG, "Gyro reading stopped by user at sample #%d", ctrl->readCount);
                    break;
                }

                // 处理陀螺仪复位事件
                if (bits & GYRO_RESET_BIT) {
                    ESP_LOGI(TAG, "Gyro reset timer triggered - performing gyroscope software reset only");
                    ESP_LOGI(TAG, "System other states remain unchanged");

                    // 执行陀螺仪复位
                    SkGyroReset();
                    ESP_LOGI(TAG, "Gyroscope software reset completed - system continues running normally");

                    // 发送陀螺仪复位信息到TCP服务器（在主任务中安全执行）
                    if (ctrl->tcpConnected && ctrl->tcpSocket >= 0) {
                        char reset_msg[128];
                        uint32_t timestamp = SkOsGetTickCnt();
                        snprintf(reset_msg, sizeof(reset_msg),
                                "FT_2:GYRO_RESET,timestamp=%lu,status=SUCCESS\n", timestamp);

                        int sent = send(ctrl->tcpSocket, reset_msg, strlen(reset_msg), 0);
                        if (sent > 0) {
                            ESP_LOGI(TAG, "Gyro reset info sent to TCP server: %s", reset_msg);
                        } else {
                            ESP_LOGW(TAG, "Failed to send gyro reset info to TCP server");
                        }
                    } else {
                        ESP_LOGW(TAG, "TCP not connected, gyro reset info not sent");
                    }
                }

                // 延时100ms
                vTaskDelay(pdMS_TO_TICKS(GYRO_READ_INTERVAL_MS));
            }

            endTime = SkOsGetTickCnt();
            ctrl->isRunning = false;

            // 停止陀螺仪复位定时器
            if (ctrl->gyroResetTimer && xTimerStop(ctrl->gyroResetTimer, 0) == pdPASS) {
                ESP_LOGI(TAG, "Gyro reset timer stopped");
            } else {
                ESP_LOGW(TAG, "Failed to stop gyro reset timer");
            }

            // 断开TCP连接并停止TCP任务
            if (ctrl->tcpConnected) {
                tcp_disconnect(ctrl->tcpSocket);
                ctrl->tcpSocket = -1;
                ctrl->tcpConnected = false;
                ESP_LOGI(TAG, "TCP connection closed");
            }

            // 停止TCP任务
            if (ctrl->tcpTaskHandle) {
                // 发送退出信号给TCP任务
                xEventGroupSetBits(ctrl->eventGroup, GYRO_TASK_EXIT_BIT);
                vTaskDelay(pdMS_TO_TICKS(100)); // 等待任务退出
                ctrl->tcpTaskHandle = NULL;
                ESP_LOGI(TAG, "TCP task stopped");
                // 清除退出信号，为下次使用做准备
                xEventGroupClearBits(ctrl->eventGroup, GYRO_TASK_EXIT_BIT);
            }

            // 打印测试结果
            ESP_LOGI(TAG, "=== Gyro Reading Results ===");
            ESP_LOGI(TAG, "Total samples: %d", ctrl->readCount);
            ESP_LOGI(TAG, "Success: %d", ctrl->successCount);
            ESP_LOGI(TAG, "Failed: %d", ctrl->failCount);
            ESP_LOGI(TAG, "TCP sent: %d", ctrl->tcpSendCount);
            ESP_LOGI(TAG, "TCP failed: %d", ctrl->tcpSendFailCount);
            ESP_LOGI(TAG, "TCP connected: %s", ctrl->tcpConnected ? "Yes" : "No");
            if (ctrl->readCount > 0) {
                ESP_LOGI(TAG, "Success rate: %.2f%%",
                         (float)ctrl->successCount / ctrl->readCount * 100.0f);
                ESP_LOGI(TAG, "Reading duration: %d ms", endTime - ctrl->startTime);
                ESP_LOGI(TAG, "Average interval: %.2f ms",
                         (float)(endTime - ctrl->startTime) / ctrl->readCount);
                if (ctrl->tcpSendCount > 0) {
                    ESP_LOGI(TAG, "TCP send rate: %.2f%%",
                             (float)ctrl->tcpSendCount / ctrl->successCount * 100.0f);
                }
            }
            ESP_LOGI(TAG, "=== Reading Session Completed ===");
        }
    }

    vTaskDelete(NULL);
}

/**
 * @brief 初始化我的应用程序
 */
void MyAppInit(void)
{
    MyAppCtrl_t* ctrl = &g_myAppCtrl;
    
    ESP_LOGI(TAG, "Initializing My App...");
    
    // 创建事件组
    ctrl->eventGroup = xEventGroupCreate();
    if (ctrl->eventGroup == NULL) {
        ESP_LOGE(TAG, "Failed to create event group");
        return;
    }
    
    // 创建按键监控任务 - 增加栈大小以避免栈溢出
    BaseType_t ret = xTaskCreate(key_monitor_task, "KeyMonitorTask", 4096,
                                 ctrl, 4, &ctrl->keyMonitorTaskHandle);
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create key monitor task");
        return;
    }

    // 创建陀螺仪读取任务
    ret = xTaskCreate(gyro_read_task, "GyroReadTask", 4096,
                      ctrl, 5, &ctrl->gyroTaskHandle);
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create gyro read task");
        return;
    }

    // TCP任务将在开始读取陀螺仪时创建

    // 初始化TCP相关变量
    ctrl->tcpSocket = -1;
    ctrl->tcpConnected = false;
    ctrl->tcpSendCount = 0;
    ctrl->tcpSendFailCount = 0;

    // 创建陀螺仪复位定时器 (1分钟间隔)
    ctrl->gyroResetTimer = xTimerCreate(
        "GyroResetTimer",                           // 定时器名称
        pdMS_TO_TICKS(GYRO_RESET_INTERVAL_MS),     // 定时器周期 (1分钟)
        pdTRUE,                                     // 自动重载
        (void*)ctrl,                                // 定时器ID (传递控制结构体指针)
        gyro_reset_timer_callback                   // 回调函数
    );

    if (ctrl->gyroResetTimer == NULL) {
        ESP_LOGE(TAG, "Failed to create gyro reset timer");
        return;
    }

    ESP_LOGI(TAG, "Gyro reset timer created - will reset every %d seconds when active", GYRO_RESET_INTERVAL_MS / 1000);
    ESP_LOGI(TAG, "My App initialized successfully");
}

/**
 * @brief 反初始化我的应用程序
 */
void MyAppDeinit(void)
{
    MyAppCtrl_t* ctrl = &g_myAppCtrl;
    
    ESP_LOGI(TAG, "Deinitializing My App...");
    
    // 发送退出信号
    if (ctrl->eventGroup) {
        xEventGroupSetBits(ctrl->eventGroup, GYRO_TASK_EXIT_BIT);
    }
    
    // 等待任务结束
    if (ctrl->gyroTaskHandle) {
        vTaskDelay(pdMS_TO_TICKS(100));
        ctrl->gyroTaskHandle = NULL;
    }

    if (ctrl->tcpTaskHandle) {
        vTaskDelay(pdMS_TO_TICKS(100));
        ctrl->tcpTaskHandle = NULL;
    }

    if (ctrl->keyMonitorTaskHandle) {
        vTaskDelete(ctrl->keyMonitorTaskHandle);
        ctrl->keyMonitorTaskHandle = NULL;
    }

    // 停止并删除陀螺仪复位定时器
    if (ctrl->gyroResetTimer) {
        xTimerStop(ctrl->gyroResetTimer, portMAX_DELAY);
        xTimerDelete(ctrl->gyroResetTimer, portMAX_DELAY);
        ctrl->gyroResetTimer = NULL;
        ESP_LOGI(TAG, "Gyro reset timer stopped and deleted");
    }

    // 清理TCP连接
    if (ctrl->tcpConnected) {
        tcp_disconnect(ctrl->tcpSocket);
        ctrl->tcpSocket = -1;
        ctrl->tcpConnected = false;
    }

    // 删除事件组
    if (ctrl->eventGroup) {
        vEventGroupDelete(ctrl->eventGroup);
        ctrl->eventGroup = NULL;
    }
    
    ESP_LOGI(TAG, "My App deinitialized");
}

/**
 * @brief 获取当前读取状态
 */
void MyAppGetStatus(void)
{
    MyAppCtrl_t* ctrl = &g_myAppCtrl;
    uint32_t currentTime = SkOsGetTickCnt();

    ESP_LOGI(TAG, "=== Current Status ===");
    ESP_LOGI(TAG, "Running: %s", ctrl->isRunning ? "Yes" : "No");
    ESP_LOGI(TAG, "Read count: %d", ctrl->readCount);
    ESP_LOGI(TAG, "Success: %d", ctrl->successCount);
    ESP_LOGI(TAG, "Failed: %d", ctrl->failCount);
    ESP_LOGI(TAG, "TCP connected: %s", ctrl->tcpConnected ? "Yes" : "No");
    ESP_LOGI(TAG, "TCP sent: %d", ctrl->tcpSendCount);
    ESP_LOGI(TAG, "TCP failed: %d", ctrl->tcpSendFailCount);
    if (ctrl->readCount > 0) {
        ESP_LOGI(TAG, "Success rate: %.2f%%",
                 (float)ctrl->successCount / ctrl->readCount * 100.0f);
        if (ctrl->tcpSendCount > 0) {
            ESP_LOGI(TAG, "TCP send rate: %.2f%%",
                     (float)ctrl->tcpSendCount / ctrl->successCount * 100.0f);
        }
    }
    if (ctrl->isRunning && ctrl->startTime > 0) {
        ESP_LOGI(TAG, "Running time: %d ms", currentTime - ctrl->startTime);
    }
}
