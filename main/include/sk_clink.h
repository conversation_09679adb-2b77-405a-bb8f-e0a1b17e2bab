/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_clink.h
 * @description: 控制链路接口定义.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#ifndef SK_CLINK_H
#define SK_CLINK_H

#include "sk_sm.h"

enum {
    CLINK_EVENT_CALL_AGENT = 1,     // 呼叫代理
    CLINK_EVENT_CALL_TERM = 2,      // 呼叫终端
    CLINK_EVENT_STATE_REPORT = 3,   // 状态上报
    CLINK_EVENT_FINISH_CALL = 4,    // 通话结束
    CLINK_EVENT_CALL_TERM_OK = 5,   // 呼叫终端响应
    CLINK_EVENT_ENTER_PM_MODE = 6,  // 进入省电模式
};

enum {
    CLINK_RUN_FLAG_IDLE = 0,
    CLINK_RUN_FLAG_START = 1,       // 开始运行
    CLINK_RUN_FLAG_STOP = 2,        // 停止运行
};

#ifdef __cplusplus
extern "C" {
#endif

int SkClinkInit(SkStateHandler handler);
void SkClinkSetFunFlag(uint8_t flag);
int SkClinkEventNotify(uint8_t event, int32_t param1, int32_t param2, int32_t param3);
void SkClinkSetPm(bool flag);
void SkClinkSetCalleeId(uint8_t calleeId[6]);

#ifdef __cplusplus
}
#endif

#endif